import React, { useEffect, useRef } from 'react';
import JsBarcode from 'jsbarcode';

// XPrinter条形码类型到JsBarcode格式的映射
const BARCODE_FORMAT_MAP = {
  '1': 'CODE128',
  '2': 'CODE39',
  '3': 'CODE93',
  '4': 'EAN13',
  '5': 'EAN8',
  '6': 'ITF',
  '7': 'ITF14',
  '8': 'UPC',
  '9': 'UPC',
  '10': 'codabar',
  '11': 'CODE128', // CHINA_POST 使用 CODE128 替代
  '12': 'ITF',     // MATRIX_25 使用 ITF 替代
  '13': 'ITF'      // INDUSTRIAL_25 使用 ITF 替代
};

const BarcodeRenderer = ({
  value = '123456789',
  format = 'CODE128',
  width = 2,
  height = 100,
  displayValue = true,
  fontSize = 20,
  textAlign = 'center',
  textPosition = 'bottom',
  background = '#ffffff',
  lineColor = '#000000',
  style = {}
}) => {
  const canvasRef = useRef(null);

  // 转换条形码格式
  const getJsBarcodeFormat = (formatInput) => {
    // 如果是数字字符串，从映射表获取
    if (typeof formatInput === 'string' && /^\d+$/.test(formatInput)) {
      return BARCODE_FORMAT_MAP[formatInput] || 'CODE128';
    }
    // 如果已经是字符串格式，直接返回
    return formatInput || 'CODE128';
  };

  // 验证条形码数据是否符合格式要求，并自动选择合适的格式
  const validateAndAdjustBarcodeFormat = (value, format) => {
    const originalFormat = getJsBarcodeFormat(format);

    console.log(`条形码验证: 内容="${value}", 原始格式="${originalFormat}"`);

    // 如果原始格式验证通过，直接使用
    if (validateBarcodeData(value, originalFormat)) {
      console.log(`格式验证通过: ${originalFormat}`);
      return { isValid: true, format: originalFormat, value: value };
    }

    // 如果原始格式不匹配，尝试自动选择合适的格式
    console.log(`格式验证失败，尝试自动选择格式...`);

    // 对于数字内容，按长度选择最合适的格式
    if (/^\d+$/.test(value)) {
      const length = value.length;

      if (length === 13) {
        return { isValid: true, format: 'EAN13', value: value };
      } else if (length === 12) {
        return { isValid: true, format: 'UPC', value: value };
      } else if (length === 8) {
        return { isValid: true, format: 'EAN8', value: value };
      } else if (length >= 6 && length % 2 === 0) {
        return { isValid: true, format: 'ITF', value: value };
      } else {
        // 其他长度的数字使用CODE128
        console.log(`数字长度${length}不符合标准格式，使用CODE128`);
        return { isValid: true, format: 'CODE128', value: value };
      }
    }

    // 非数字内容使用CODE128
    console.log(`非数字内容，使用CODE128`);
    return { isValid: true, format: 'CODE128', value: value };
  };

  // 基础验证函数
  const validateBarcodeData = (value, format) => {
    switch (format) {
      case 'EAN13':
        return /^\d{13}$/.test(value);
      case 'EAN8':
        return /^\d{8}$/.test(value);
      case 'UPC':
        return /^\d{12}$/.test(value);
      case 'CODE39':
        return /^[0-9A-Z\-\.\s\$\/\+%]+$/.test(value);
      case 'ITF':
      case 'ITF14':
        return /^\d+$/.test(value) && value.length % 2 === 0;
      default:
        return value && value.length > 0;
    }
  };

  useEffect(() => {
    if (canvasRef.current && value) {
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');

      // 计算合适的Canvas尺寸
      const canvasWidth = Math.max(200, width * 100);
      const canvasHeight = Math.max(60, height + (displayValue ? fontSize + 10 : 0));

      // 设置Canvas的实际尺寸
      canvas.width = canvasWidth;
      canvas.height = canvasHeight;

      // 清除之前的内容
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // 验证并调整条形码格式
      const validation = validateAndAdjustBarcodeFormat(value, format);
      console.log(`条形码渲染: 输入格式=${format}, 最终格式=${validation.format}, 内容=${validation.value}, Canvas尺寸=${canvasWidth}x${canvasHeight}`);

      if (!validation.isValid) {
        console.warn(`条形码数据无法处理: ${value}`);
        ctx.fillStyle = '#ff6600';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`数据格式不正确`, canvas.width / 2, canvas.height / 2 - 10);
        ctx.fillText(`${format}: ${value}`, canvas.width / 2, canvas.height / 2 + 10);
        return;
      }

      try {
        // 使用验证后的格式和值生成条码
        JsBarcode(canvas, validation.value, {
          format: validation.format,
          width: width,
          height: height,
          displayValue: displayValue,
          fontSize: fontSize,
          textAlign: textAlign,
          textPosition: textPosition,
          background: background,
          lineColor: lineColor,
          margin: 0
        });
        console.log(`条形码生成成功: ${validation.format} - ${validation.value}`);
      } catch (error) {
        console.error('条码生成失败:', error);

        // 如果生成失败，显示错误信息
        ctx.fillStyle = '#ff0000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(`条码生成失败`, canvas.width / 2, canvas.height / 2 - 10);
        ctx.fillText(`${error.message}`, canvas.width / 2, canvas.height / 2 + 10);
      }
    }
  }, [value, format, width, height, displayValue, fontSize, textAlign, textPosition, background, lineColor]);

  return (
    <canvas
      ref={canvasRef}
      style={{
        maxWidth: '100%',
        maxHeight: '100%',
        objectFit: 'contain',
        display: 'block',
        backgroundColor: background,
        ...style
      }}
    />
  );
};

export default BarcodeRenderer;
