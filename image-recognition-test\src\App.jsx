import React, { useState, useEffect } from 'react';
import { Layout, Typography, Space, Card, Row, Col, Button, Modal, Input, Form, message, Divider, Alert, Tabs, Select, Radio } from 'antd';
import { SettingOutlined, InfoCircleOutlined, GithubOutlined, BarcodeOutlined, RobotOutlined, ExperimentOutlined } from '@ant-design/icons';
import ImageUpload from './components/ImageUpload';
import LabelRenderer from './components/LabelRenderer';
import BarcodeTest from './components/BarcodeTest';
import TestRenderer from './pages/TestRenderer';
import textInApi from './services/textinApi';
import dataConverter from './services/dataConverter';
import llmVisionApi from './services/llmVisionApi';
import llmDataConverter from './services/llmDataConverter';
import './App.css';

const { Header, Content, Footer } = Layout;
const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

function App() {
  const [loading, setLoading] = useState(false);
  const [recognitionResult, setRecognitionResult] = useState(null);
  const [convertedData, setConvertedData] = useState(null);
  const [canvasWidth, setCanvasWidth] = useState(800);
  const [canvasHeight, setCanvasHeight] = useState(600);
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [apiMode, setApiMode] = useState('llm'); // 默认使用大模型
  const [credentials, setCredentials] = useState({
    appId: '',
    secretCode: ''
  });
  const [llmConfig, setLlmConfig] = useState({
    provider: 'doubao',
    apiKey: '',
    sparkAuth: {
      appId: '4ada222d',
      apiSecret: 'ZWI2YjY2OTQ0NjcyZjNlMWQ3ODlhNzM4',
      apiKey: '6cbe2b0468fce889711ae469250a47ca'
    }
  });

  // 立即设置默认的豆包认证信息
  React.useEffect(() => {
    console.log('设置默认的大模型提供商为豆包');
    // 默认使用豆包，但需要用户配置API Key
  }, []);

  // 从localStorage加载配置
  useEffect(() => {
    // 加载TextIn凭据
    const savedCredentials = localStorage.getItem('textin-credentials');
    if (savedCredentials) {
      try {
        const parsed = JSON.parse(savedCredentials);
        setCredentials(parsed);
        textInApi.setCredentials(parsed.appId, parsed.secretCode);
      } catch (error) {
        console.error('加载TextIn凭据失败:', error);
      }
    }

    // 加载大模型配置
    const savedLlmConfig = localStorage.getItem('llm-config');
    if (savedLlmConfig) {
      try {
        const parsed = JSON.parse(savedLlmConfig);
        setLlmConfig(parsed);

        if (parsed.provider === 'spark' && parsed.sparkAuth) {
          llmVisionApi.setSparkAuth(
            parsed.sparkAuth.appId,
            parsed.sparkAuth.apiSecret,
            parsed.sparkAuth.apiKey
          );
        } else {
          llmVisionApi.setConfig(parsed.provider, parsed.apiKey);
        }
      } catch (error) {
        console.error('加载大模型配置失败:', error);
      }
    }

    // 加载API模式
    const savedApiMode = localStorage.getItem('api-mode');
    if (savedApiMode) {
      setApiMode(savedApiMode);
    }
  }, []);

  // 单独的useEffect来设置默认的星火认证信息
  useEffect(() => {
    // 如果没有保存的配置，使用默认的星火认证信息
    const savedLlmConfig = localStorage.getItem('llm-config');
    if (!savedLlmConfig && llmConfig.sparkAuth) {
      console.log('设置默认星火认证信息:', llmConfig.sparkAuth);
      llmVisionApi.setSparkAuth(
        llmConfig.sparkAuth.appId,
        llmConfig.sparkAuth.apiSecret,
        llmConfig.sparkAuth.apiKey
      );
    } else if (savedLlmConfig) {
      // 如果有保存的配置，确保也设置到API中
      try {
        const parsed = JSON.parse(savedLlmConfig);
        if (parsed.provider === 'spark' && parsed.sparkAuth) {
          console.log('重新设置保存的星火认证信息:', parsed.sparkAuth);
          llmVisionApi.setSparkAuth(
            parsed.sparkAuth.appId,
            parsed.sparkAuth.apiSecret,
            parsed.sparkAuth.apiKey
          );
        }
      } catch (error) {
        console.error('重新设置保存的配置失败:', error);
      }
    }
  }, [llmConfig]);

  const handleUploadSuccess = (data) => {
    console.log('App.jsx: 识别成功', data);

    // 添加详细的调试信息
    console.log('=== App.jsx 数据流调试 ===');
    console.log('1. 原始识别数据:', data);

    if (data && data.result && data.result.pages && data.result.pages[0]) {
      console.log('2. 页面数据:', data.result.pages[0]);
      console.log('3. 页面尺寸:', data.result.pages[0].width, 'x', data.result.pages[0].height);
      console.log('4. 页面元素数量:', data.result.pages[0].elements?.length || 0);

      // 分析原始元素数据
      if (data.result.pages[0].elements) {
        console.log('5. 原始元素详情:');
        data.result.pages[0].elements.forEach((element, index) => {
          if (String(element.elementType) === '1') {
            console.log(`  文本${index}: "${element.content}" at (${element.x}, ${element.y}) size ${element.width}x${element.height}`);
          }
        });
      }
    }

    let finalData;
    let canvasW = 800;
    let canvasH = 600;

    if (apiMode === 'llm') {
      // LLM模式，调用转换器然后重新注入画布信息
      console.log('6. 开始LLM数据转换...');
      finalData = llmDataConverter.convertToXPrinter(data);
      console.log('7. 转换后数据:', finalData);

      if (data && data.result && data.result.pages && data.result.pages[0]) {
        const page = data.result.pages[0];
        canvasW = page.width || 800;
        canvasH = page.height || 600;
        console.log('8. 提取的画布尺寸:', canvasW, 'x', canvasH);

        const hasCanvas = finalData.some(el => String(el.elementType) === '3');
        if (!hasCanvas) {
          console.log('9. 添加画布元素');
          finalData.unshift({
            elementType: '3',
            width: String(canvasW),
            height: String(canvasH),
            os: 'web',
            versionCode: '0'
          });
        }
      }
    } else {
      // TextIn API 模式
      finalData = dataConverter.convertToXPrinter(data);
      if (data && data.result && data.result.pages && data.result.pages[0]) {
        canvasW = data.result.pages[0].width || 800;
        canvasH = data.result.pages[0].height || 600;
      }
    }

    console.log('10. 最终数据:', finalData);
    console.log('11. 最终画布尺寸:', canvasW, 'x', canvasH);

    // 分析最终文本元素
    const finalTextElements = finalData.filter(el => String(el.elementType) === '1');
    console.log('12. 最终文本元素:');
    finalTextElements.forEach((element, index) => {
      console.log(`  文本${index}: "${element.content}" at (${element.x}, ${element.y}) size ${element.width}x${element.height}`);
    });

    setConvertedData(finalData);
    setCanvasWidth(canvasW);
    setCanvasHeight(canvasH);
    message.success('数据已成功加载到预览区');
  };

  const handleUploadError = (error) => {
    console.error('识别失败:', error);
    setRecognitionResult(null);
    setConvertedData(null);
  };

  const handleDataChange = (newData) => {
    console.log('数据已更新:', newData);
    setConvertedData(newData);
  };

  const handleSaveCredentials = (values) => {
    try {
      if (values.appId !== undefined) {
        // 保存TextIn凭据
        setCredentials(values);
        textInApi.setCredentials(values.appId, values.secretCode);
        localStorage.setItem('textin-credentials', JSON.stringify(values));
      }

      if (values.provider !== undefined) {
        // 保存大模型配置
        const newLlmConfig = {
          provider: values.provider,
          apiKey: values.llmApiKey || '',
          sparkAuth: values.provider === 'spark' ? {
            appId: values.sparkAppId || llmConfig.sparkAuth.appId,
            apiSecret: values.sparkApiSecret || llmConfig.sparkAuth.apiSecret,
            apiKey: values.sparkApiKey || llmConfig.sparkAuth.apiKey
          } : llmConfig.sparkAuth
        };

        setLlmConfig(newLlmConfig);

        if (values.provider === 'spark') {
          llmVisionApi.setSparkAuth(
            newLlmConfig.sparkAuth.appId,
            newLlmConfig.sparkAuth.apiSecret,
            newLlmConfig.sparkAuth.apiKey
          );
        } else {
          llmVisionApi.setConfig(values.provider, values.llmApiKey);
        }

        localStorage.setItem('llm-config', JSON.stringify(newLlmConfig));
      }

      setSettingsVisible(false);
      message.success('配置保存成功');
    } catch (error) {
      console.error('保存配置失败:', error);
      message.error('保存配置失败');
    }
  };

  const handleApiModeChange = (mode) => {
    setApiMode(mode);
    localStorage.setItem('api-mode', mode);
  };

  const downloadJson = (data, filename) => {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{
        background: '#fff',
        padding: '0 24px',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between'
      }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
            图像识别测试平台
          </Title>
          <Text type="secondary" style={{ marginLeft: 16 }}>
            基于 TextIn API 的文档解析与标签生成
          </Text>
        </div>
        <Space>
          <Button
            icon={<SettingOutlined />}
            onClick={() => setSettingsVisible(true)}
          >
            API设置
          </Button>
          <Button
            icon={<InfoCircleOutlined />}
            type="link"
            href="https://www.textin.com/document/pdf_to_markdown"
            target="_blank"
          >
            API文档
          </Button>
        </Space>
      </Header>

      <Content style={{ padding: '24px' }}>
        <div style={{ maxWidth: 1400, margin: '0 auto' }}>
          {/* API模式选择 */}
          <Card size="small" style={{ marginBottom: 24 }}>
            <Space>
              <Text strong>识别方式:</Text>
              <Radio.Group value={apiMode} onChange={(e) => handleApiModeChange(e.target.value)}>
                <Radio.Button value="textin">TextIn API</Radio.Button>
                <Radio.Button value="llm">大模型 API</Radio.Button>
              </Radio.Group>
            </Space>
          </Card>

          {/* API状态提示 */}
          {apiMode === 'textin' ? (
            !credentials.appId || !credentials.secretCode ? (
              <Alert
                message="请先配置 TextIn API 凭据"
                description="点击右上角的 API设置 按钮配置您的 TextIn API 凭据后才能使用识别功能。"
                type="warning"
                showIcon
                style={{ marginBottom: 24 }}
                action={
                  <Button size="small" onClick={() => setSettingsVisible(true)}>
                    立即配置
                  </Button>
                }
              />
            ) : (
              <Alert
                message="TextIn API 已配置"
                description="您可以开始上传文件进行识别了。"
                type="success"
                showIcon
                style={{ marginBottom: 24 }}
              />
            )
          ) : (
            !llmConfig.apiKey ? (
              <Alert
                message="请先配置大模型 API 密钥"
                description="点击右上角的 API设置 按钮配置您的大模型 API 密钥后才能使用识别功能。"
                type="warning"
                showIcon
                style={{ marginBottom: 24 }}
                action={
                  <Button size="small" onClick={() => setSettingsVisible(true)}>
                    立即配置
                  </Button>
                }
              />
            ) : (
              <Alert
                message={`${llmVisionApi.getProviders().find(p => p.key === llmConfig.provider)?.name || '大模型'} API 已配置`}
                description="您可以开始上传文件进行识别了。"
                type="success"
                showIcon
                style={{ marginBottom: 24 }}
              />
            )
          )}

          <Tabs
            defaultActiveKey="recognition"
            items={[
              {
                key: 'recognition',
                label: '图像识别',
                children: (
                  <Row gutter={[24, 24]}>
                    {/* 左侧：文件上传 */}
                    <Col xs={24} lg={8}>
                      <ImageUpload
                        onUploadSuccess={handleUploadSuccess}
                        onUploadError={handleUploadError}
                        loading={loading}
                        setLoading={setLoading}
                        apiMode={apiMode}
                      />
                    </Col>

                    {/* 右侧：结果展示 */}
                    <Col xs={24} lg={16}>
                      {convertedData ? (
                        <LabelRenderer
                          data={convertedData}
                          canvasWidth={canvasWidth}
                          canvasHeight={canvasHeight}
                          onDataChange={handleDataChange}
                        />
                      ) : (
                        <Card title="标签预览">
                          <div style={{
                            textAlign: 'center',
                            padding: '60px 20px',
                            color: '#999'
                          }}>
                            <InfoCircleOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                            <Paragraph>
                              请上传图片或文档文件开始识别
                            </Paragraph>
                            <Text type="secondary">
                              支持 PNG、JPG、PDF 等格式，识别后将显示标签预览
                            </Text>
                          </div>
                        </Card>
                      )}
                    </Col>
                  </Row>
                )
              },
              {
                key: 'barcode-test',
                label: (
                  <span>
                    <BarcodeOutlined />
                    条形码测试
                  </span>
                ),
                children: <BarcodeTest />
              },
              {
                key: 'test-renderer',
                label: (
                  <span>
                    <ExperimentOutlined />
                    渲染测试
                  </span>
                ),
                children: <TestRenderer />
              }
            ]}
          />

          {/* 数据下载区域 */}
          {(recognitionResult || convertedData) && (
            <>
              <Divider />
              <Card title="数据下载" style={{ marginTop: 24 }}>
                <Space wrap>
                  {recognitionResult && (
                    <Button
                      type="primary"
                      onClick={() => downloadJson(recognitionResult, 'textin-result.json')}
                    >
                      下载 TextIn 原始数据
                    </Button>
                  )}
                  {convertedData && (
                    <Button
                      type="primary"
                      onClick={() => downloadJson(convertedData, 'xprinter-data.json')}
                    >
                      下载 XPrinter 格式数据
                    </Button>
                  )}
                </Space>
              </Card>
            </>
          )}
        </div>
      </Content>

      <Footer style={{ textAlign: 'center', background: '#fafafa' }}>
        <Space split={<Divider type="vertical" />}>
          <Text type="secondary">
            图像识别测试平台 ©2024
          </Text>
          <Text type="secondary">
            基于 TextIn API 构建
          </Text>
          <a href="https://github.com" target="_blank" rel="noopener noreferrer">
            <GithubOutlined /> GitHub
          </a>
        </Space>
      </Footer>

      {/* API设置模态框 */}
      <Modal
        title="API 设置"
        open={settingsVisible}
        onCancel={() => setSettingsVisible(false)}
        footer={null}
        width={600}
      >
        <Tabs
          items={[
            {
              key: 'textin',
              label: 'TextIn API',
              children: (
                <Form
                  layout="vertical"
                  initialValues={credentials}
                  onFinish={handleSaveCredentials}
                >
                  <Alert
                    message="获取 TextIn API 凭据"
                    description={
                      <div>
                        请登录 <a href="https://www.textin.com" target="_blank" rel="noopener noreferrer">TextIn 官网</a>，
                        前往 工作台-账号设置-开发者信息 查看您的 API 凭据。
                      </div>
                    }
                    type="info"
                    showIcon
                    style={{ marginBottom: 16 }}
                  />

                  <Form.Item
                    label="App ID"
                    name="appId"
                    rules={[{ required: true, message: '请输入 App ID' }]}
                  >
                    <Input placeholder="请输入您的 TextIn App ID" />
                  </Form.Item>

                  <Form.Item
                    label="Secret Code"
                    name="secretCode"
                    rules={[{ required: true, message: '请输入 Secret Code' }]}
                  >
                    <Input.Password placeholder="请输入您的 TextIn Secret Code" />
                  </Form.Item>

                  <Form.Item>
                    <Button type="primary" htmlType="submit" block>
                      保存 TextIn 配置
                    </Button>
                  </Form.Item>
                </Form>
              )
            },
            {
              key: 'llm',
              label: (
                <span>
                  <RobotOutlined />
                  大模型 API
                </span>
              ),
              children: (
                <Form
                  layout="vertical"
                  initialValues={{
                    provider: llmConfig.provider,
                    llmApiKey: llmConfig.apiKey,
                    sparkAppId: llmConfig.sparkAuth?.appId || '',
                    sparkApiSecret: llmConfig.sparkAuth?.apiSecret || '',
                    sparkApiKey: llmConfig.sparkAuth?.apiKey || ''
                  }}
                  onFinish={handleSaveCredentials}
                >
                  <Alert
                    message="大模型 API 配置"
                    description="选择您要使用的大模型提供商并配置相应的认证信息。星火大模型在识别中文文本样式方面表现优秀。"
                    type="info"
                    showIcon
                    style={{ marginBottom: 16 }}
                  />

                  <Form.Item
                    label="提供商"
                    name="provider"
                    rules={[{ required: true, message: '请选择提供商' }]}
                  >
                    <Select placeholder="选择大模型提供商">
                      {llmVisionApi.getProviders().map(provider => (
                        <Option key={provider.key} value={provider.key}>
                          {provider.name} ({provider.model})
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => prevValues.provider !== currentValues.provider}>
                    {({ getFieldValue }) => {
                      const provider = getFieldValue('provider');

                      if (provider === 'spark') {
                        return (
                          <>
                            <Alert
                              message="星火认证信息"
                              description="星火大模型使用 APPID、APISecret 和 APIKey 进行认证。请从讯飞开放平台获取这些信息。"
                              type="success"
                              showIcon
                              style={{ marginBottom: 16 }}
                            />

                            <Form.Item
                              label="APPID"
                              name="sparkAppId"
                              rules={[{ required: true, message: '请输入 APPID' }]}
                            >
                              <Input placeholder="请输入星火 APPID" />
                            </Form.Item>

                            <Form.Item
                              label="APISecret"
                              name="sparkApiSecret"
                              rules={[{ required: true, message: '请输入 APISecret' }]}
                            >
                              <Input.Password placeholder="请输入星火 APISecret" />
                            </Form.Item>

                            <Form.Item
                              label="APIKey"
                              name="sparkApiKey"
                              rules={[{ required: true, message: '请输入 APIKey' }]}
                            >
                              <Input.Password placeholder="请输入星火 APIKey" />
                            </Form.Item>
                          </>
                        );
                      } else {
                        return (
                          <Form.Item
                            label="API 密钥"
                            name="llmApiKey"
                            rules={[{ required: true, message: '请输入 API 密钥' }]}
                          >
                            <Input.Password placeholder="请输入您的 API 密钥" />
                          </Form.Item>
                        );
                      }
                    }}
                  </Form.Item>

                  <Form.Item>
                    <Button type="primary" htmlType="submit" block>
                      保存大模型配置
                    </Button>
                  </Form.Item>
                </Form>
              )
            }
          ]}
        />
      </Modal>
    </Layout>
  );
}

export default App;
