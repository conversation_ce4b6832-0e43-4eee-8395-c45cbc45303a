/**
 * 大模型数据转换器
 * 将大模型返回的数据转换为标准的XPrinter格式
 */

class LLMDataConverter {
  constructor() {
    this.canvasWidth = 800;
    this.canvasHeight = 600;
  }

  /**
   * 转换大模型数据为XPrinter格式
   * @param {Object} llmData - 大模型返回的数据
   * @returns {Array} XPrinter格式的数据数组
   */
  convertToXPrinter(llmData) {
    try {
      console.log('开始转换大模型数据:', llmData);

      if (!llmData.result || !llmData.result.pages) {
        throw new Error('大模型数据格式不正确');
      }

      const result = [];
      const pages = llmData.result.pages;

      // 获取像素密度和转换比例
      const pixelDensity = llmData.result.pixelDensity || 203; // 默认203 PPI
      const conversionRatio = llmData.result.conversionRatio || (25.4 / pixelDensity); // mm per pixel

      console.log('使用像素密度:', pixelDensity, 'PPI');
      console.log('转换比例:', conversionRatio, 'mm/pixel');

      // 处理每一页
      pages.forEach((page, pageIndex) => {
        console.log(`处理第${pageIndex + 1}页:`, page);

        // 设置画布尺寸
        if (page.width && page.height) {
          this.canvasWidth = page.width;
          this.canvasHeight = page.height;
        }

        // 处理页面元素，传递转换比例
        if (page.elements && Array.isArray(page.elements)) {
          page.elements.forEach(element => {
            const convertedElement = this.convertElement(element, conversionRatio);
            if (convertedElement) {
              result.push(convertedElement);
            }
          });
        }
      });

      // 计算画布尺寸（像素转毫米，使用动态转换比例）
      const canvasWidthMm = Math.round(this.canvasWidth * conversionRatio * 100) / 100;
      const canvasHeightMm = Math.round(this.canvasHeight * conversionRatio * 100) / 100;

      // 返回标准的XPrinter格式
      const xprinterResult = {
        width: canvasWidthMm,    // 画布宽度（毫米）
        height: canvasHeightMm,  // 画布高度（毫米）
        data: result             // XPrinter数据数组
      };

      console.log(`画布尺寸: ${this.canvasWidth}px × ${this.canvasHeight}px -> ${canvasWidthMm}mm × ${canvasHeightMm}mm`);
      console.log(`使用转换比例: ${conversionRatio.toFixed(4)} mm/pixel (${pixelDensity} PPI)`);
      console.log('大模型数据转换完成，XPrinter格式结果:', xprinterResult);

      return xprinterResult;
    } catch (error) {
      console.error('大模型数据转换失败:', error);
      throw new Error(`大模型数据转换失败: ${error.message}`);
    }
  }

  /**
   * 转换单个元素
   * @param {Object} element - 大模型返回的元素
   * @param {number} conversionRatio - 像素到毫米的转换比例 (mm/pixel)
   * @returns {Object} 转换后的XPrinter元素
   */
  convertElement(element, conversionRatio = 25.4 / 203) {
    // 将像素值转换为毫米值
    const convertPxToMm = (pxValue) => {
      const px = parseFloat(pxValue) || 0;
      return Math.round(px * conversionRatio * 1000) / 1000; // 保留3位小数
    };

    // 确保必需的字段存在，并进行像素到毫米的转换
    const convertedElement = {
      elementType: element.elementType || 1,
      x: this.ensureString(convertPxToMm(element.x || '0')),
      y: this.ensureString(convertPxToMm(element.y || '0')),
      width: this.ensureString(convertPxToMm(element.width || '100')),
      height: this.ensureString(convertPxToMm(element.height || '20')),
      rotationAngle: this.ensureString(element.rotationAngle || '0'),
      lockLocation: this.ensureString(element.lockLocation || 'false'),
      takePrint: this.ensureString(element.takePrint || 'true'),
      mirrorImage: this.ensureString(element.mirrorImage || 'false')
    };

    console.log(`元素转换: 像素(${element.x || 0}, ${element.y || 0}, ${element.width || 100}×${element.height || 20}) -> 毫米(${convertedElement.x}, ${convertedElement.y}, ${convertedElement.width}×${convertedElement.height})`);

    // 根据元素类型添加特定属性
    switch (String(element.elementType)) {
      case '1': // 文本
        return this.convertTextElement(element, convertedElement, conversionRatio);
      case '2': // 条形码
        return this.convertBarcodeElement(element, convertedElement, conversionRatio);
      case '3': // 画布
        return this.convertCanvasElement(element, convertedElement, conversionRatio);
      case '6': // 图片
        return this.convertImageElement(element, convertedElement, conversionRatio);
      case '7': // 二维码
        return this.convertQRCodeElement(element, convertedElement, conversionRatio);
      default:
        console.warn('未知的元素类型:', element.elementType);
        return convertedElement;
    }
  }

  /**
   * 转换文本元素
   * @param {Object} element - 原始元素
   * @param {Object} base - 基础属性（已转换为毫米）
   * @param {number} conversionRatio - 像素到毫米的转换比例
   * @returns {Object} 转换后的文本元素
   */
  convertTextElement(element, base, conversionRatio) {
    const text = element.content || '';

    // 字体大小也需要从像素转换为毫米
    const fontSizePx = parseFloat(element.textSize || '12.0');
    const fontSizeMm = Math.round(fontSizePx * conversionRatio * 1000) / 1000;

    console.log(`文本字体大小转换: ${fontSizePx}px -> ${fontSizeMm}mm`);

    return {
      ...base,
      content: text,
      textSize: this.ensureString(fontSizeMm.toString()),
      hAlignment: this.ensureString(element.hAlignment || '1'),
      bold: this.ensureString(element.bold || 'false'),
      italic: this.ensureString(element.italic || 'false'),
      underline: this.ensureString(element.underline || 'false'),
      strikethrough: this.ensureString(element.strikethrough || 'false'),
      wordSpace: this.ensureString(element.wordSpace || '0.0'),
      linesSpace: this.ensureString(element.linesSpace || '0.0'),
      fontType: this.ensureString(element.fontType || '-2'),
      blackWhiteReflection: this.ensureString(element.blackWhiteReflection || 'false'),
      automaticHeightCalculation: this.ensureString(element.automaticHeightCalculation || 'true'),
      lineWrap: this.ensureString(element.lineWrap || 'true'),
      flipX: this.ensureString(element.flipX || 'false'),
      // XPrinter必需字段
      inputDataType: this.ensureString(element.inputDataType || '1'),
      transmutationType: element.transmutationType || 1,
      transmutationValue: this.ensureString(element.transmutationValue || '1'),
      transmutationCount: this.ensureString(element.transmutationCount || '0'),
      transmutationNegativeNumbers: this.ensureString(element.transmutationNegativeNumbers || 'false'),
      excelPos: element.excelPos || -1,
      bindKey: element.bindKey || '',
      controlType: this.ensureString(element.controlType || '3')
    };
  }

  /**
   * 转换条形码元素
   * @param {Object} element - 原始元素
   * @param {Object} base - 基础属性（已转换为毫米）
   * @param {number} conversionRatio - 像素到毫米的转换比例
   * @returns {Object} 转换后的条形码元素
   */
  convertBarcodeElement(element, base, conversionRatio) {
    // 条形码文本字体大小也需要转换
    const fontSizePx = parseFloat(element.textSize || '11.0');
    const fontSizeMm = Math.round(fontSizePx * conversionRatio * 1000) / 1000;

    return {
      ...base,
      content: element.content || '',
      barcodeType: this.ensureString(element.barcodeType || '1'),
      showText: this.ensureString(element.showText || '3'),
      textAlignment: element.textAlignment || 1,
      horizontalAlignment: this.ensureString(element.horizontalAlignment || 'true'),
      inputDataType: this.ensureString(element.inputDataType || '1'),
      transmutationValue: this.ensureString(element.transmutationValue || '1'),
      transmutationCount: this.ensureString(element.transmutationCount || '0'),
      transmutationType: element.transmutationType || 1,
      transmutationNegativeNumbers: this.ensureString(element.transmutationNegativeNumbers || 'false'),
      excelPos: element.excelPos || -1,
      bindKey: element.bindKey || '',
      showKeyName: element.showKeyName || false,
      textSize: this.ensureString(fontSizeMm.toString()),
      fontType: this.ensureString(element.fontType || '-2'),
      bold: this.ensureString(element.bold || 'false'),
      italic: this.ensureString(element.italic || 'false'),
      underline: this.ensureString(element.underline || 'false'),
      strikethrough: this.ensureString(element.strikethrough || 'false'),
      controlType: this.ensureString(element.controlType || '2')
    };
  }

  /**
   * 转换画布元素
   * @param {Object} element - 原始元素
   * @param {Object} base - 基础属性（已转换为毫米）
   * @param {number} conversionRatio - 像素到毫米的转换比例
   * @returns {Object} 转换后的画布元素
   */
  convertCanvasElement(element, base, conversionRatio) {
    // 画布尺寸使用已转换的毫米值
    const canvasWidthMm = Math.round(this.canvasWidth * conversionRatio * 100) / 100;
    const canvasHeightMm = Math.round(this.canvasHeight * conversionRatio * 100) / 100;

    return {
      elementType: 3,
      os: element.os || 'web',
      versionCode: element.versionCode || 0,
      cableLabelDirection: element.cableLabelDirection || 2,
      cableLabelLength: element.cableLabelLength || 0,
      width: this.ensureString(element.width || canvasWidthMm.toString()),
      height: this.ensureString(element.height || canvasHeightMm.toString()),
      dpi: element.dpi || 144,
      labelType: element.labelType || 1,
      blackMarkHeight: element.blackMarkHeight || 0,
      blackMarkOffset: element.blackMarkOffset || 0,
      blackMarkDirection: element.blackMarkDirection || 0
    };
  }

  /**
   * 转换图片元素
   * @param {Object} element - 原始元素
   * @param {Object} base - 基础属性（已转换为毫米）
   * @param {number} conversionRatio - 像素到毫米的转换比例
   * @returns {Object} 转换后的图片元素
   */
  convertImageElement(element, base, conversionRatio) {
    return {
      ...base,
      content: element.content || '',
      colorMode: this.ensureString(element.colorMode || '0'),
      grayValue: this.ensureString(element.grayValue || '128'),
      tile: this.ensureString(element.tile || 'false'),
      blackWhiteReflection: this.ensureString(element.blackWhiteReflection || 'false')
    };
  }

  /**
   * 转换二维码元素
   * @param {Object} element - 原始元素
   * @param {Object} base - 基础属性（已转换为毫米）
   * @param {number} conversionRatio - 像素到毫米的转换比例
   * @returns {Object} 转换后的二维码元素
   */
  convertQRCodeElement(element, base, conversionRatio) {
    return {
      ...base,
      content: element.content || '',
      codeType: element.codeType || 'QR_CODE',
      whiteMargin: this.ensureString(element.whiteMargin || '0'),
      errorCorrectionLevel: element.errorCorrectionLevel || 'M',
      inputDataType: this.ensureString(element.inputDataType || '1'),
      prefix: element.prefix || '',
      suffix: element.suffix || '',
      transmutationValue: this.ensureString(element.transmutationValue || '1'),
      transmutationCount: this.ensureString(element.transmutationCount || '0'),
      transmutationType: element.transmutationType || 1,
      transmutationNegativeNumbers: this.ensureString(element.transmutationNegativeNumbers || 'false'),
      excelPos: element.excelPos || -1,
      bindKey: element.bindKey || '',
      showKeyName: element.showKeyName || false
    };
  }

  /**
   * 确保值为字符串类型
   * @param {any} value - 输入值
   * @returns {string} 字符串值
   */
  ensureString(value) {
    return String(value);
  }

  /**
   * 计算合理的文字宽度，避免不必要的换行
   * @param {string} text - 文字内容
   * @param {number} originalWidth - 原始识别的宽度
   * @param {number} fontSize - 字体大小
   * @returns {number} 调整后的宽度
   */
  calculateTextWidth(text, originalWidth, fontSize = 12.0) {
    // 此函数估算不准确，已停用。直接使用大模型返回的宽度。
    return originalWidth;
    /*
    if (!text || text.length === 0) {
      return Math.max(originalWidth, 20); // 最小宽度20
    }

    // 估算字符宽度：中文字符约等于字体大小，英文字符约为字体大小的0.6倍
    const chineseCharCount = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    const otherCharCount = text.length - chineseCharCount;

    const estimatedWidth = chineseCharCount * fontSize + otherCharCount * fontSize * 0.6;

    // 取原始宽度和估算宽度的较大值，并添加一些边距
    const adjustedWidth = Math.max(originalWidth, estimatedWidth * 1.2);

    // 设置最小和最大宽度限制
    const minWidth = Math.max(20, fontSize * 2); // 最小宽度至少是字体大小的2倍
    const maxWidth = this.canvasWidth * 0.9; // 不超过画布宽度的90%

    return Math.max(minWidth, Math.min(maxWidth, adjustedWidth));
    */
  }
}

// 创建单例实例
const llmDataConverter = new LLMDataConverter();

export default llmDataConverter;
