import React, { useState, useCallback } from 'react';
import { Upload, Button, message, Card, Image, Space, Typography, Alert, InputNumber, Select, Row, Col, Divider } from 'antd';
import { InboxOutlined, UploadOutlined, DeleteOutlined, EyeOutlined, MobileOutlined } from '@ant-design/icons';
import llmVisionApi from '../services/llmVisionApi';

const { Dragger } = Upload;
const { Title, Text } = Typography;
const { Option } = Select;

const ImageUploadLLM = ({ onUploadSuccess, onUploadError, loading, setLoading }) => {
  const [fileList, setFileList] = useState([]);
  const [previewImage, setPreviewImage] = useState('');
  const [previewVisible, setPreviewVisible] = useState(false);
  
  // 像素密度配置
  const [pixelDensity, setPixelDensity] = useState(414); // 默认小米13的PPI
  const [customPPI, setCustomPPI] = useState(false);
  
  // 标签物理尺寸配置
  const [usePhysicalSize, setUsePhysicalSize] = useState(true); // 默认使用物理尺寸
  const [targetWidth, setTargetWidth] = useState(40); // 目标宽度(mm)
  const [targetHeight, setTargetHeight] = useState(30); // 目标高度(mm)

  // 常见手机型号的PPI预设
  const phonePresets = [
    { id: 'xiaomi13', label: '小米13', value: 414, brand: 'Xiaomi' },
    { id: 'xiaomi12', label: '小米12', value: 419, brand: 'Xiaomi' },
    { id: 'iphone15pro', label: 'iPhone 15 Pro', value: 460, brand: 'Apple' },
    { id: 'iphone14', label: 'iPhone 14', value: 460, brand: 'Apple' },
    { id: 'galaxys23', label: 'Samsung Galaxy S23', value: 425, brand: 'Samsung' },
    { id: 'p60pro', label: 'Huawei P60 Pro', value: 440, brand: 'Huawei' },
    { id: 'custom', label: '自定义', value: 'custom', brand: 'Custom' }
  ];

  // 常见标签尺寸预设
  const labelSizePresets = [
    { id: 'small', label: '小商品标签', width: 40, height: 30 },
    { id: 'medium', label: '中等标签', width: 60, height: 40 },
    { id: 'large', label: '大标签', width: 80, height: 60 },
    { id: 'express', label: '快递标签', width: 100, height: 80 },
    { id: 'custom', label: '自定义尺寸', width: 'custom', height: 'custom' }
  ];

  // 处理手机型号选择
  const handlePhonePresetChange = useCallback((value) => {
    if (value === 'custom') {
      setCustomPPI(true);
      setPixelDensity(300); // 自定义时的默认值
    } else {
      setCustomPPI(false);
      setPixelDensity(value);
    }
  }, []);

  // 处理自定义PPI输入
  const handleCustomPPIChange = useCallback((value) => {
    if (value && value > 0) {
      setPixelDensity(value);
    }
  }, []);

  // 处理标签尺寸预设选择
  const handleLabelSizeChange = useCallback((value) => {
    const preset = labelSizePresets.find(p => p.id === value);
    if (preset) {
      if (preset.width === 'custom') {
        // 自定义尺寸，保持当前值
      } else {
        setTargetWidth(preset.width);
        setTargetHeight(preset.height);
      }
    }
  }, []);

  // 计算基于目标物理尺寸的转换比例
  const calculatePhysicalConversionRatio = useCallback((imageWidth, imageHeight) => {
    // 根据图片尺寸和目标物理尺寸计算转换比例
    const ratioX = targetWidth / imageWidth;  // mm/pixel
    const ratioY = targetHeight / imageHeight; // mm/pixel
    
    // 使用较小的比例以保持宽高比
    return Math.min(ratioX, ratioY);
  }, [targetWidth, targetHeight]);

  // 文件上传前的验证
  const beforeUpload = useCallback((file) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件！');
      return false;
    }

    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('图片大小不能超过 10MB！');
      return false;
    }

    return false; // 阻止自动上传
  }, []);

  // 处理文件选择
  const handleChange = useCallback((info) => {
    let newFileList = [...info.fileList];
    
    // 只保留最新的一个文件
    newFileList = newFileList.slice(-1);
    
    // 为文件生成预览URL
    newFileList = newFileList.map(file => {
      if (file.originFileObj && !file.url && !file.preview) {
        file.url = URL.createObjectURL(file.originFileObj);
      }
      return file;
    });

    setFileList(newFileList);
  }, []);

  // 删除文件
  const handleRemove = useCallback((file) => {
    console.log('删除文件:', file);

    // 释放预览URL
    if (file.url && file.url.startsWith('blob:')) {
      URL.revokeObjectURL(file.url);
    }

    setFileList([]);
    setPreviewImage('');
    setPreviewVisible(false);
  }, []);

  // 预览图片
  const handlePreview = useCallback((file) => {
    setPreviewImage(file.url || file.preview);
    setPreviewVisible(true);
  }, []);

  // 识别处理
  const handleRecognize = useCallback(async () => {
    if (fileList.length === 0) {
      message.warning('请先选择文件');
      return;
    }

    const file = fileList[0].originFileObj;
    if (!file) {
      message.error('文件对象不存在');
      return;
    }

    // 验证配置
    if (!usePhysicalSize && (!pixelDensity || pixelDensity <= 0)) {
      message.error('请设置有效的像素密度');
      return;
    }
    
    if (usePhysicalSize && (!targetWidth || !targetHeight || targetWidth <= 0 || targetHeight <= 0)) {
      message.error('请设置有效的标签物理尺寸');
      return;
    }

    setLoading(true);

    try {
      console.log('开始使用大模型识别文件:', file.name);
      if (usePhysicalSize) {
        console.log('使用标签物理尺寸:', targetWidth, 'x', targetHeight, 'mm');
      } else {
        console.log('使用像素密度:', pixelDensity, 'PPI');
      }
      message.loading('正在使用大模型识别中，请稍候...', 0);

      let result;

      // 将文件转换为base64，并获取图片尺寸
      const { base64, size } = await new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          const dataUrl = e.target.result;
          const img = document.createElement('img');
          img.onload = () => {
            resolve({
              base64: dataUrl,
              size: { width: img.naturalWidth, height: img.naturalHeight }
            });
          };
          img.onerror = reject;
          img.src = dataUrl;
        };
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });

      console.log('图片识别尺寸:', size);
      
      let conversionRatio;
      if (usePhysicalSize) {
        // 使用目标物理尺寸计算转换比例
        conversionRatio = calculatePhysicalConversionRatio(size.width, size.height);
        console.log('使用标签物理尺寸转换:', `目标尺寸 ${targetWidth}×${targetHeight}mm, 转换比例 ${conversionRatio.toFixed(4)} mm/pixel`);
      } else {
        // 使用像素密度计算转换比例
        conversionRatio = 25.4 / pixelDensity;
        console.log('使用像素密度转换:', `${pixelDensity} PPI, 转换比例 ${conversionRatio.toFixed(4)} mm/pixel`);
      }
      
      result = await llmVisionApi.recognizeImage(base64, size);
      
      // 在结果中添加转换信息，供后续转换使用
      if (result && result.result) {
        result.result.pixelDensity = usePhysicalSize ? Math.round(25.4 / conversionRatio) : pixelDensity;
        result.result.conversionRatio = conversionRatio;
        result.result.usePhysicalSize = usePhysicalSize;
        if (usePhysicalSize) {
          result.result.targetWidth = targetWidth;
          result.result.targetHeight = targetHeight;
        }
      }

      message.destroy();
      message.success('识别完成！');

      if (onUploadSuccess) {
        onUploadSuccess(result, file);
      }
    } catch (error) {
      console.error('识别失败:', error);
      message.destroy();
      message.error(`识别失败: ${error.message}`);

      if (onUploadError) {
        onUploadError(error);
      }
    } finally {
      setLoading(false);
    }
  }, [fileList, setLoading, onUploadSuccess, onUploadError, usePhysicalSize, pixelDensity, targetWidth, targetHeight, calculatePhysicalConversionRatio]);

  return (
    <Space direction="vertical" style={{ width: '100%' }} size="large">
      <Card title="大模型图像识别" size="small">
        <Space direction="vertical" style={{ width: '100%' }} size="middle">
          {/* 文件上传区域 */}
          <Dragger
            fileList={fileList}
            beforeUpload={beforeUpload}
            onChange={handleChange}
            onRemove={handleRemove}
            onPreview={handlePreview}
            accept="image/*"
            listType="picture"
            maxCount={1}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽图片到此区域上传</p>
            <p className="ant-upload-hint">
              支持单个图片上传，支持 JPG、PNG、GIF 等格式，文件大小不超过 10MB
            </p>
          </Dragger>

          {/* 预览功能 */}
          {fileList.length > 0 && (
            <Space>
              <Button 
                icon={<EyeOutlined />} 
                onClick={() => handlePreview(fileList[0])}
              >
                预览图片
              </Button>
              <Button 
                icon={<DeleteOutlined />} 
                onClick={() => handleRemove(fileList[0])}
                danger
              >
                删除图片
              </Button>
            </Space>
          )}

          {/* 尺寸转换方式选择 */}
          <Card size="small" title={
            <Space>
              <MobileOutlined />
              <span>尺寸转换配置</span>
            </Space>
          }>
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              <Alert
                message="选择转换方式"
                description="选择如何将图片像素尺寸转换为实际物理尺寸。推荐使用'标签物理尺寸'方式获得更准确的结果。"
                type="info"
                showIcon
                style={{ marginBottom: 8 }}
              />

              {/* 转换方式选择 */}
              <div>
                <Text strong>转换方式:</Text>
                <div style={{ marginTop: 8 }}>
                  <Space direction="vertical">
                    <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                      <input
                        type="radio"
                        checked={!usePhysicalSize}
                        onChange={() => setUsePhysicalSize(false)}
                        style={{ marginRight: 8 }}
                      />
                      <span>手机像素密度转换（适用于手机拍摄的图片）</span>
                    </label>
                    <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
                      <input
                        type="radio"
                        checked={usePhysicalSize}
                        onChange={() => setUsePhysicalSize(true)}
                        style={{ marginRight: 8 }}
                      />
                      <span>标签物理尺寸转换（推荐，适用于扫描图或下载的图片）</span>
                    </label>
                  </Space>
                </div>
              </div>

              {/* 手机像素密度配置 */}
              {!usePhysicalSize && (
                <div style={{ background: '#f0f9ff', padding: '12px', borderRadius: '6px' }}>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Text strong>手机型号:</Text>
                      <Select
                        style={{ width: '100%', marginTop: 4 }}
                        placeholder="选择手机型号"
                        value={customPPI ? 'custom' : pixelDensity}
                        onChange={handlePhonePresetChange}
                        size="small"
                      >
                        {phonePresets.map(preset => (
                          <Option key={preset.id} value={preset.value}>
                            {preset.label} {preset.value !== 'custom' && `(${preset.value} PPI)`}
                          </Option>
                        ))}
                      </Select>
                    </Col>

                    <Col span={12}>
                      <Text strong>像素密度 (PPI):</Text>
                      <InputNumber
                        style={{ width: '100%', marginTop: 4 }}
                        placeholder="输入PPI值"
                        value={pixelDensity}
                        onChange={handleCustomPPIChange}
                        disabled={!customPPI}
                        min={50}
                        max={1000}
                        precision={0}
                        size="small"
                      />
                    </Col>
                  </Row>

                  <div style={{
                    background: '#fff',
                    padding: '6px 10px',
                    borderRadius: '4px',
                    fontSize: '11px',
                    color: '#666',
                    marginTop: '8px'
                  }}>
                    <Text type="secondary">
                      转换比例: 1英寸 = {pixelDensity} 像素 = 25.4 毫米
                      {pixelDensity > 0 && ` (1像素 ≈ ${(25.4 / pixelDensity).toFixed(3)}毫米)`}
                    </Text>
                  </div>
                </div>
              )}

              {/* 标签物理尺寸配置 */}
              {usePhysicalSize && (
                <div style={{ background: '#f6ffed', padding: '12px', borderRadius: '6px' }}>
                  <div style={{ marginBottom: '12px' }}>
                    <Text strong>标签类型:</Text>
                    <Select
                      style={{ width: '100%', marginTop: 4 }}
                      placeholder="选择标签类型"
                      onChange={handleLabelSizeChange}
                      size="small"
                    >
                      {labelSizePresets.map(preset => (
                        <Option key={preset.id} value={preset.id}>
                          {preset.label} {preset.width !== 'custom' && `(${preset.width}×${preset.height}mm)`}
                        </Option>
                      ))}
                    </Select>
                  </div>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Text strong>目标宽度 (mm):</Text>
                      <InputNumber
                        style={{ width: '100%', marginTop: 4 }}
                        placeholder="宽度"
                        value={targetWidth}
                        onChange={setTargetWidth}
                        min={5}
                        max={500}
                        precision={1}
                        size="small"
                      />
                    </Col>

                    <Col span={12}>
                      <Text strong>目标高度 (mm):</Text>
                      <InputNumber
                        style={{ width: '100%', marginTop: 4 }}
                        placeholder="高度"
                        value={targetHeight}
                        onChange={setTargetHeight}
                        min={5}
                        max={500}
                        precision={1}
                        size="small"
                      />
                    </Col>
                  </Row>

                  <div style={{
                    background: '#fff',
                    padding: '6px 10px',
                    borderRadius: '4px',
                    fontSize: '11px',
                    color: '#666',
                    marginTop: '8px'
                  }}>
                    <Text type="secondary">
                      目标尺寸: {targetWidth}mm × {targetHeight}mm
                      （转换比例将根据图片尺寸自动计算）
                    </Text>
                  </div>
                </div>
              )}
            </Space>
          </Card>

          <Button
            type="primary"
            icon={<UploadOutlined />}
            size="large"
            loading={loading}
            disabled={
              fileList.length === 0 ||
              (!usePhysicalSize && (!pixelDensity || pixelDensity <= 0)) ||
              (usePhysicalSize && (!targetWidth || !targetHeight || targetWidth <= 0 || targetHeight <= 0))
            }
            onClick={handleRecognize}
            style={{ width: '100%' }}
          >
            {loading ? '识别中...' : '开始识别'}
          </Button>
        </Space>
      </Card>

      {/* 图片预览模态框 */}
      <Image
        width={200}
        style={{ display: 'none' }}
        src={previewImage}
        preview={{
          visible: previewVisible,
          src: previewImage,
          onVisibleChange: (visible) => setPreviewVisible(visible),
        }}
      />
    </Space>
  );
};

export default ImageUploadLLM;
