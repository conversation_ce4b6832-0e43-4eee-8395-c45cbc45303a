<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渲染改进测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-container {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .canvas-area {
            position: relative;
            width: 400px;
            height: 300px;
            background-color: #ffffff;
            border: 2px solid #1890ff;
            margin: 20px auto;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .text-element {
            position: absolute;
            border: 1px dashed #1890ff;
            background-color: rgba(24, 144, 255, 0.1);
            padding: 2px;
            display: flex;
            align-items: center;
            box-sizing: border-box;
            overflow: hidden;
        }
        .left-align { justify-content: flex-start; text-align: left; }
        .center-align { justify-content: center; text-align: center; }
        .right-align { justify-content: flex-end; text-align: right; }
        
        .text-content {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            width: 100%;
        }
        
        .info {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>渲染改进测试</h1>
    <p>测试字体大小匹配、宽度计算、背景显示和文字对齐等改进效果。</p>

    <div class="test-container">
        <h3>测试1: 字体大小匹配</h3>
        <div class="canvas-area">
            <!-- 小字体文本 -->
            <div class="text-element left-align" style="left: 10px; top: 20px; width: 120px; height: 20px; font-size: 10px;">
                <div class="text-content">小字体文本</div>
            </div>
            
            <!-- 中等字体文本 -->
            <div class="text-element left-align" style="left: 10px; top: 60px; width: 150px; height: 30px; font-size: 16px;">
                <div class="text-content">中等字体文本</div>
            </div>
            
            <!-- 大字体文本 -->
            <div class="text-element left-align" style="left: 10px; top: 110px; width: 200px; height: 40px; font-size: 24px;">
                <div class="text-content">大字体文本</div>
            </div>
        </div>
        <div class="info">测试不同字体大小的渲染效果，字体大小应该与元素高度匹配</div>
    </div>

    <div class="test-container">
        <h3>测试2: 文字对齐方式</h3>
        <div class="canvas-area">
            <!-- 左对齐 -->
            <div class="text-element left-align" style="left: 20px; top: 30px; width: 150px; height: 25px; font-size: 14px;">
                <div class="text-content">左对齐文本</div>
            </div>
            
            <!-- 居中对齐 -->
            <div class="text-element center-align" style="left: 125px; top: 80px; width: 150px; height: 25px; font-size: 14px;">
                <div class="text-content">居中对齐文本</div>
            </div>
            
            <!-- 右对齐 -->
            <div class="text-element right-align" style="left: 230px; top: 130px; width: 150px; height: 25px; font-size: 14px;">
                <div class="text-content">右对齐文本</div>
            </div>
        </div>
        <div class="info">测试不同对齐方式：左对齐(hAlignment=1)、居中(hAlignment=2)、右对齐(hAlignment=3)</div>
    </div>

    <div class="test-container">
        <h3>测试3: 宽度计算优化</h3>
        <div class="canvas-area">
            <!-- 短文本，宽度应该足够 -->
            <div class="text-element left-align" style="left: 20px; top: 30px; width: 80px; height: 25px; font-size: 14px;">
                <div class="text-content">短文本</div>
            </div>
            
            <!-- 中等长度文本 -->
            <div class="text-element left-align" style="left: 20px; top: 80px; width: 180px; height: 25px; font-size: 14px;">
                <div class="text-content">这是中等长度的文本</div>
            </div>
            
            <!-- 长文本，应该不换行 -->
            <div class="text-element left-align" style="left: 20px; top: 130px; width: 300px; height: 25px; font-size: 14px;">
                <div class="text-content">这是一段比较长的文本内容，应该不会换行显示</div>
            </div>
        </div>
        <div class="info">测试宽度计算是否合理，文本应该在一行内显示，不会换行</div>
    </div>

    <div class="test-container">
        <h3>测试4: 中英文混合</h3>
        <div class="canvas-area">
            <!-- 中英文混合，居中对齐 -->
            <div class="text-element center-align" style="left: 50px; top: 50px; width: 300px; height: 30px; font-size: 16px;">
                <div class="text-content">Product Name 产品名称 123</div>
            </div>
            
            <!-- 英文为主 -->
            <div class="text-element left-align" style="left: 50px; top: 100px; width: 250px; height: 25px; font-size: 14px;">
                <div class="text-content">English Text with 中文</div>
            </div>
            
            <!-- 数字和符号 -->
            <div class="text-element right-align" style="left: 100px; top: 150px; width: 200px; height: 25px; font-size: 14px;">
                <div class="text-content">Price: ¥99.99</div>
            </div>
        </div>
        <div class="info">测试中英文混合文本的渲染效果和宽度计算</div>
    </div>

    <script>
        // 模拟字体大小估算
        function estimateFontSize(height) {
            return Math.max(8, Math.min(72, Math.round(height * 0.75)));
        }

        // 模拟宽度计算
        function calculateTextWidth(text, originalWidth, fontSize) {
            if (!text) return Math.max(originalWidth, 20);
            
            const chineseCharCount = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
            const otherCharCount = text.length - chineseCharCount;
            
            const estimatedWidth = chineseCharCount * fontSize + otherCharCount * fontSize * 0.6;
            const adjustedWidth = Math.max(originalWidth, estimatedWidth * 1.2);
            
            return Math.max(fontSize * 2, Math.min(800 * 0.9, adjustedWidth));
        }

        // 在控制台输出测试结果
        console.log('=== 渲染改进测试结果 ===');
        
        const testCases = [
            { text: '短文本', height: 20 },
            { text: '这是中等长度的文本', height: 25 },
            { text: '这是一段比较长的文本内容，应该不会换行显示', height: 25 },
            { text: 'Product Name 产品名称 123', height: 30 },
            { text: 'English Text with 中文', height: 25 }
        ];
        
        testCases.forEach((testCase, index) => {
            const fontSize = estimateFontSize(testCase.height);
            const width = calculateTextWidth(testCase.text, 100, fontSize);
            
            console.log(`测试${index + 1}: "${testCase.text}"`);
            console.log(`  高度: ${testCase.height}px, 估算字体: ${fontSize}px, 计算宽度: ${Math.round(width)}px`);
        });
    </script>
</body>
</html>
