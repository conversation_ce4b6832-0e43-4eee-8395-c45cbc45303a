// 测试文字宽度计算功能
// 由于是Node.js环境，我们需要直接测试计算逻辑

class TestDataConverter {
  constructor() {
    this.canvasWidth = 800;
    this.canvasHeight = 600;
  }

  calculateBounds(pos) {
    if (!pos || pos.length !== 8) {
      return { x: 0, y: 0, width: 100, height: 20 };
    }

    const xs = [pos[0], pos[2], pos[4], pos[6]];
    const ys = [pos[1], pos[3], pos[5], pos[7]];

    const minX = Math.min(...xs);
    const maxX = Math.max(...xs);
    const minY = Math.min(...ys);
    const maxY = Math.max(...ys);

    return {
      x: Math.round(minX),
      y: Math.round(minY),
      width: Math.round(maxX - minX),
      height: Math.round(maxY - minY)
    };
  }

  calculateTextWidth(text, originalWidth, fontSize = 12.0) {
    if (!text || text.length === 0) {
      return Math.max(originalWidth, 20); // 最小宽度20
    }

    // 估算字符宽度：中文字符约等于字体大小，英文字符约为字体大小的0.6倍
    const chineseCharCount = (text.match(/[\u4e00-\u9fa5]/g) || []).length;
    const otherCharCount = text.length - chineseCharCount;

    const estimatedWidth = chineseCharCount * fontSize + otherCharCount * fontSize * 0.6;

    // 取原始宽度和估算宽度的较大值，并添加一些边距
    const adjustedWidth = Math.max(originalWidth, estimatedWidth * 1.1);

    // 设置最小和最大宽度限制
    const minWidth = 20;
    const maxWidth = this.canvasWidth * 0.8; // 不超过画布宽度的80%

    return Math.max(minWidth, Math.min(maxWidth, adjustedWidth));
  }

  convertTextLine(textLine) {
    const bounds = this.calculateBounds(textLine.pos);
    const text = textLine.text || '';

    // 计算合理的文字宽度，避免不必要的换行
    const adjustedWidth = this.calculateTextWidth(text, bounds.width, 12.0);

    return {
      elementType: 1,
      x: bounds.x.toString(),
      y: bounds.y.toString(),
      width: adjustedWidth.toString(),
      height: bounds.height.toString(),
      content: text,
      lineWrap: 'true'
    };
  }
}

const dataConverter = new TestDataConverter();

// 模拟TextIn识别结果，包含可能导致换行的文字
const testTextLine = {
  type: 'line',
  text: '这是一段比较长的中文文字内容，可能会导致换行问题',
  pos: [10, 10, 150, 10, 150, 30, 10, 30], // 宽度只有140，可能不够
  angle: 0
};

// 设置画布尺寸
dataConverter.canvasWidth = 800;
dataConverter.canvasHeight = 600;

console.log('原始文字:', testTextLine.text);
console.log('原始位置:', testTextLine.pos);

// 计算原始边界框
const bounds = dataConverter.calculateBounds(testTextLine.pos);
console.log('原始边界框:', bounds);

// 转换文本行
const convertedElement = dataConverter.convertTextLine(testTextLine);
console.log('转换后的元素:', {
  content: convertedElement.content,
  x: convertedElement.x,
  y: convertedElement.y,
  width: convertedElement.width,
  height: convertedElement.height,
  lineWrap: convertedElement.lineWrap
});

// 测试不同长度的文字
const testCases = [
  '短文字',
  '这是中等长度的文字内容',
  '这是一段很长很长的中文文字内容，用来测试宽度计算是否正确',
  'Short English text',
  'This is a longer English text to test width calculation',
  '中英混合 Mixed Chinese and English text 测试'
];

console.log('\n=== 测试不同长度文字的宽度计算 ===');
testCases.forEach((text, index) => {
  const testLine = {
    ...testTextLine,
    text: text
  };
  
  const element = dataConverter.convertTextLine(testLine);
  console.log(`测试${index + 1}: "${text}"`);
  console.log(`  原始宽度: ${bounds.width}, 调整后宽度: ${element.width}`);
  console.log('');
});
