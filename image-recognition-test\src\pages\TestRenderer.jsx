import React, { useState, useCallback } from 'react';
import { Card, Input, Button, Space, message, Row, Col, Typography, Divider, InputNumber, Select, Alert } from 'antd';
import { MobileOutlined, SwapOutlined } from '@ant-design/icons';
import LabelRenderer from '../components/LabelRenderer';

const { TextArea } = Input;
const { Title, Text } = Typography;
const { Option } = Select;

const TestRenderer = () => {
  // Web显示尺寸（像素）
  const [canvasWidth, setCanvasWidth] = useState('400');
  const [canvasHeight, setCanvasHeight] = useState('300');

  // 物理尺寸（毫米）
  const [physicalWidth, setPhysicalWidth] = useState('40');
  const [physicalHeight, setPhysicalHeight] = useState('30');

  // 像素密度配置
  const [pixelDensity, setPixelDensity] = useState(414); // 默认小米13
  const [customPPI, setCustomPPI] = useState(false);

  // 其他状态
  const [jsonData, setJsonData] = useState('');
  const [renderData, setRenderData] = useState(null);
  const [loading, setLoading] = useState(false);

  // 常见手机型号的PPI预设（与ImageUpload保持一致）
  const phonePresets = [
    { id: 'xiaomi13', label: '小米13', value: 414, brand: 'Xiaomi' },
    { id: 'xiaomi12', label: '小米12', value: 419, brand: 'Xiaomi' },
    { id: 'iphone15pro', label: 'iPhone 15 Pro', value: 460, brand: 'Apple' },
    { id: 'iphone14', label: 'iPhone 14', value: 460, brand: 'Apple' },
    { id: 'galaxys23', label: 'Samsung Galaxy S23', value: 425, brand: 'Samsung' },
    { id: 'p60pro', label: 'Huawei P60 Pro', value: 440, brand: 'Huawei' },
    { id: 'custom', label: '自定义', value: 'custom', brand: 'Custom' }
  ];

  // 计算转换比例
  const conversionRatio = 25.4 / pixelDensity; // mm per pixel
  const pixelsPerMm = pixelDensity / 25.4; // pixels per mm

  // 处理手机型号选择
  const handlePhonePresetChange = useCallback((value) => {
    if (value === 'custom') {
      setCustomPPI(true);
      setPixelDensity(300); // 自定义时的默认值
    } else {
      setCustomPPI(false);
      setPixelDensity(value);
    }
  }, []);

  // 处理自定义PPI输入
  const handleCustomPPIChange = useCallback((value) => {
    if (value && value > 0) {
      setPixelDensity(value);
    }
  }, []);

  // 物理尺寸转像素尺寸
  const mmToPx = useCallback((mm) => {
    return Math.round(parseFloat(mm || 0) * pixelsPerMm);
  }, [pixelsPerMm]);

  // 像素尺寸转物理尺寸
  const pxToMm = useCallback((px) => {
    return Math.round(parseFloat(px || 0) * conversionRatio * 100) / 100;
  }, [conversionRatio]);

  // 处理物理尺寸变化
  const handlePhysicalSizeChange = useCallback((type, value) => {
    if (type === 'width') {
      setPhysicalWidth(value);
      setCanvasWidth(mmToPx(value).toString());
    } else {
      setPhysicalHeight(value);
      setCanvasHeight(mmToPx(value).toString());
    }
  }, [mmToPx]);

  // 处理像素尺寸变化
  const handlePixelSizeChange = useCallback((type, value) => {
    if (type === 'width') {
      setCanvasWidth(value);
      setPhysicalWidth(pxToMm(value).toString());
    } else {
      setCanvasHeight(value);
      setPhysicalHeight(pxToMm(value).toString());
    }
  }, [pxToMm]);

  // 示例数据 - 使用标准XPrinter格式
  const exampleData = {
    "width": 40,
    "height": 30,
    "data": [
    {
      "os": "android",
      "templateBg": "",
      "cableLabelDirection": 2,
      "cableLabelLength": 0,
      "elementType": "3",
      "versionCode": 0
    },
    {
      "lineWrap": "true",
      "underline": "false",
      "transmutationType": 1,
      "lockLocation": "false",
      "prefix": "",
      "showKeyName": false,
      "transmutationValue": "1",
      "suffix": "",
      "italic": "false",
      "mirrorImage": "false",
      "content": "卡装1克马卡龙",
      "automaticHeightCalculation": "true",
      "textArrangementType": 0,
      "transmutationCount": "0",
      "strikethrough": "false",
      "flipX": "false",
      "transmutationNegativeNumbers": "false",
      "height": "5.046296",
      "inputDataType": "1",
      "linesSpace": "0.0",
      "fontType": "-2",
      "takePrint": "true",
      "textSize": "11.0",
      "bold": "false",
      "excelPos": -1,
      "arcAngle": 180,
      "bindKey": "",
      "controlType": "3",
      "rotationAngle": "0",
      "x": "7.4629717",
      "width": "28.37963",
      "y": "3.0184822",
      "hAlignment": "1",
      "elementType": 1,
      "blackWhiteReflection": "false",
      "wordSpace": "0.0"
    },
    {
      "lineWrap": "true",
      "underline": "false",
      "transmutationType": 1,
      "lockLocation": "false",
      "prefix": "",
      "showKeyName": false,
      "transmutationValue": "1",
      "suffix": "",
      "italic": "false",
      "mirrorImage": "false",
      "content": "规格:1*30卡",
      "automaticHeightCalculation": "true",
      "textArrangementType": 0,
      "transmutationCount": "0",
      "strikethrough": "false",
      "flipX": "false",
      "transmutationNegativeNumbers": "false",
      "height": "5.509259",
      "inputDataType": "1",
      "linesSpace": "0.0",
      "fontType": "-2",
      "takePrint": "true",
      "textSize": "12.0",
      "bold": "false",
      "excelPos": -1,
      "arcAngle": 180,
      "bindKey": "",
      "controlType": "3",
      "rotationAngle": "0",
      "x": "8.972213",
      "width": "28.10185",
      "y": "7.722258",
      "hAlignment": "1",
      "elementType": 1,
      "blackWhiteReflection": "false",
      "wordSpace": "0.0"
    },
    {
      "underline": "false",
      "transmutationType": 1,
      "lockLocation": "false",
      "showText": "3",
      "barcodeType": "4",
      "showKeyName": false,
      "transmutationValue": "1",
      "italic": "false",
      "mirrorImage": "false",
      "content": "6975370866829",
      "transmutationCount": "0",
      "strikethrough": "false",
      "transmutationNegativeNumbers": "false",
      "height": "13.564815",
      "inputDataType": "1",
      "fontType": "-2",
      "takePrint": "true",
      "textSize": "11.0",
      "bold": "false",
      "excelPos": -1,
      "bindKey": "",
      "horizontalAlignment": "true",
      "controlType": "2",
      "rotationAngle": "0",
      "textAlignment": 1,
      "x": "3.0740921",
      "width": "31.898148",
      "y": "14.2315",
      "elementType": 2
    }
    ]
  };

  const handleRender = () => {
    if (!jsonData.trim()) {
      message.error('请输入JSON数据');
      return;
    }

    if (!canvasWidth || !canvasHeight) {
      message.error('请输入画布宽度和高度');
      return;
    }

    setLoading(true);
    
    try {
      const parsedData = JSON.parse(jsonData);

      // 支持三种格式：
      // 1. 标准XPrinter格式：{width, height, data}
      // 2. XPrinter原生格式：直接是元素数组
      // 3. 包装格式：{elements: [...]}
      let renderConfig;

      if (parsedData.width && parsedData.height && parsedData.data && Array.isArray(parsedData.data)) {
        // 标准XPrinter格式：{width, height, data}
        renderConfig = {
          width: parsedData.width,
          height: parsedData.height,
          data: parsedData.data
        };
        console.log('检测到标准XPrinter格式');
      } else if (Array.isArray(parsedData)) {
        // XPrinter原生格式：直接是元素数组
        renderConfig = {
          width: 50,  // 默认画布尺寸（毫米）
          height: 30,
          data: parsedData
        };
        console.log('检测到XPrinter原生数组格式');
      } else if (parsedData.elements && Array.isArray(parsedData.elements)) {
        // 包装格式：{elements: [...]}
        renderConfig = {
          width: 50,  // 默认画布尺寸（毫米）
          height: 30,
          data: parsedData.elements
        };
        console.log('检测到包装格式');
      } else {
        throw new Error('JSON数据必须是标准XPrinter格式{width, height, data}、元素数组或包含elements数组的对象');
      }

      // 设置Web显示尺寸
      renderConfig.canvasWidth = parseInt(canvasWidth);
      renderConfig.canvasHeight = parseInt(canvasHeight);

      console.log('解析的数据:', renderConfig);
      setRenderData(renderConfig);
      message.success('渲染成功！');
    } catch (error) {
      console.error('JSON解析错误:', error);
      message.error(`JSON格式错误: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const loadExample = () => {
    setJsonData(JSON.stringify(exampleData, null, 2));

    // 从示例数据中获取或推算XPrinter画布尺寸（毫米）
    let canvasWidthMm = exampleData.width || 40;
    let canvasHeightMm = exampleData.height || 30;

    // 设置物理尺寸
    setPhysicalWidth(canvasWidthMm.toString());
    setPhysicalHeight(canvasHeightMm.toString());

    // 根据当前像素密度计算对应的显示尺寸
    const displayWidth = mmToPx(canvasWidthMm);
    const displayHeight = mmToPx(canvasHeightMm);

    setCanvasWidth(displayWidth.toString());
    setCanvasHeight(displayHeight.toString());

    message.success(`示例数据已加载！物理尺寸: ${canvasWidthMm}mm × ${canvasHeightMm}mm，显示尺寸: ${displayWidth}px × ${displayHeight}px`);
  };

  const clearData = () => {
    setJsonData('');
    setRenderData(null);

    // 重置为默认尺寸
    setPhysicalWidth('40');
    setPhysicalHeight('30');
    setCanvasWidth('400');
    setCanvasHeight('300');

    message.success('数据已清空');
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1400px', margin: '0 auto' }}>
      <Title level={2}>XPrinter 数据渲染测试</Title>
      <Text type="secondary">
        输入画布尺寸和XPrinter JSON数据，点击渲染按钮查看效果
      </Text>
      
      <Row gutter={[24, 24]} style={{ marginTop: '20px' }}>
        {/* 左侧输入区域 */}
        <Col xs={24} lg={12}>
          <Card title="输入参数" size="small">
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              {/* 像素密度配置 */}
              <div>
                <Text strong>
                  <MobileOutlined /> 像素密度配置
                </Text>
                <Alert
                  message="配置手机像素密度，用于物理尺寸和显示尺寸的转换"
                  type="info"
                  showIcon
                  size="small"
                  style={{ marginTop: '8px', marginBottom: '12px' }}
                />

                <Row gutter={8}>
                  <Col span={12}>
                    <Text type="secondary">手机型号:</Text>
                    <Select
                      style={{ width: '100%', marginTop: 4 }}
                      placeholder="选择手机型号"
                      value={customPPI ? 'custom' : pixelDensity}
                      onChange={handlePhonePresetChange}
                      size="small"
                    >
                      {phonePresets.map(preset => (
                        <Option key={preset.id} value={preset.value}>
                          {preset.label} {preset.value !== 'custom' && `(${preset.value} PPI)`}
                        </Option>
                      ))}
                    </Select>
                  </Col>

                  <Col span={12}>
                    <Text type="secondary">像素密度 (PPI):</Text>
                    <InputNumber
                      style={{ width: '100%', marginTop: 4 }}
                      placeholder="输入PPI值"
                      value={pixelDensity}
                      onChange={handleCustomPPIChange}
                      disabled={!customPPI}
                      min={50}
                      max={1000}
                      precision={0}
                      size="small"
                    />
                  </Col>
                </Row>

                <div style={{
                  background: '#f6f6f6',
                  padding: '6px 10px',
                  borderRadius: '4px',
                  fontSize: '11px',
                  color: '#666',
                  marginTop: '8px'
                }}>
                  <Text type="secondary">
                    转换比例: 1英寸 = {pixelDensity} 像素 = 25.4 毫米
                    {pixelDensity > 0 && ` (1像素 ≈ ${conversionRatio.toFixed(3)}毫米)`}
                  </Text>
                </div>
              </div>

              {/* 画布尺寸配置 */}
              <div>
                <Text strong>画布尺寸配置</Text>
                <Alert
                  message="可以输入物理尺寸（毫米）或显示尺寸（像素），系统会自动同步转换"
                  type="info"
                  showIcon
                  size="small"
                  style={{ marginTop: '8px', marginBottom: '12px' }}
                />

                {/* 物理尺寸（毫米） */}
                <div style={{ marginBottom: '12px' }}>
                  <Text type="secondary">物理尺寸 (毫米):</Text>
                  <Row gutter={8} style={{ marginTop: '4px' }}>
                    <Col span={11}>
                      <InputNumber
                        placeholder="宽度(mm)"
                        value={physicalWidth}
                        onChange={(value) => handlePhysicalSizeChange('width', value)}
                        addonBefore="宽"
                        style={{ width: '100%' }}
                        precision={1}
                        min={1}
                        max={1000}
                        size="small"
                      />
                    </Col>
                    <Col span={2} style={{ textAlign: 'center', paddingTop: '4px' }}>
                      <Text type="secondary">×</Text>
                    </Col>
                    <Col span={11}>
                      <InputNumber
                        placeholder="高度(mm)"
                        value={physicalHeight}
                        onChange={(value) => handlePhysicalSizeChange('height', value)}
                        addonBefore="高"
                        style={{ width: '100%' }}
                        precision={1}
                        min={1}
                        max={1000}
                        size="small"
                      />
                    </Col>
                  </Row>
                </div>

                {/* 转换指示器 */}
                <div style={{ textAlign: 'center', margin: '8px 0' }}>
                  <SwapOutlined style={{ color: '#1890ff', fontSize: '16px' }} />
                  <Text type="secondary" style={{ marginLeft: '8px', fontSize: '12px' }}>
                    自动转换
                  </Text>
                </div>

                {/* 显示尺寸（像素） */}
                <div>
                  <Text type="secondary">显示尺寸 (像素):</Text>
                  <Row gutter={8} style={{ marginTop: '4px' }}>
                    <Col span={11}>
                      <InputNumber
                        placeholder="宽度(px)"
                        value={canvasWidth}
                        onChange={(value) => handlePixelSizeChange('width', value)}
                        addonBefore="宽"
                        style={{ width: '100%' }}
                        precision={0}
                        min={50}
                        max={5000}
                        size="small"
                      />
                    </Col>
                    <Col span={2} style={{ textAlign: 'center', paddingTop: '4px' }}>
                      <Text type="secondary">×</Text>
                    </Col>
                    <Col span={11}>
                      <InputNumber
                        placeholder="高度(px)"
                        value={canvasHeight}
                        onChange={(value) => handlePixelSizeChange('height', value)}
                        addonBefore="高"
                        style={{ width: '100%' }}
                        precision={0}
                        min={50}
                        max={5000}
                        size="small"
                      />
                    </Col>
                  </Row>
                </div>
              </div>

              {/* JSON数据输入 */}
              <div>
                <Text strong>XPrinter JSON 数据</Text>
                <TextArea
                  value={jsonData}
                  onChange={(e) => setJsonData(e.target.value)}
                  placeholder="请输入XPrinter格式的JSON数据..."
                  rows={15}
                  style={{ marginTop: '8px', fontFamily: 'monospace' }}
                />
              </div>

              {/* 操作按钮 */}
              <Space>
                <Button 
                  type="primary" 
                  onClick={handleRender}
                  loading={loading}
                >
                  渲染
                </Button>
                <Button onClick={loadExample}>
                  加载示例
                </Button>
                <Button onClick={clearData}>
                  清空
                </Button>
              </Space>
            </Space>
          </Card>
        </Col>

        {/* 右侧渲染区域 */}
        <Col xs={24} lg={12}>
          <Card title="渲染结果" size="small">
            {renderData ? (
              <div style={{ textAlign: 'center' }}>
                <LabelRenderer
                  data={renderData}
                  canvasWidth={renderData.canvasWidth}
                  canvasHeight={renderData.canvasHeight}
                  onDataChange={() => {}} // 测试页面不需要数据变更
                />
                <Divider />
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  {/* 尺寸信息 */}
                  <div style={{ background: '#f8f9fa', padding: '12px', borderRadius: '6px' }}>
                    <Text strong style={{ color: '#1890ff' }}>尺寸信息</Text>
                    <div style={{ marginTop: '8px' }}>
                      <Row gutter={16}>
                        <Col span={12}>
                          <Text type="secondary" style={{ fontSize: '12px' }}>物理尺寸:</Text>
                          <div>
                            <Text strong>{renderData.width}mm × {renderData.height}mm</Text>
                          </div>
                        </Col>
                        <Col span={12}>
                          <Text type="secondary" style={{ fontSize: '12px' }}>显示尺寸:</Text>
                          <div>
                            <Text strong>{renderData.canvasWidth}px × {renderData.canvasHeight}px</Text>
                          </div>
                        </Col>
                      </Row>
                    </div>
                  </div>

                  {/* 转换信息 */}
                  <div style={{ background: '#f0f9ff', padding: '12px', borderRadius: '6px' }}>
                    <Text strong style={{ color: '#1890ff' }}>转换信息</Text>
                    <div style={{ marginTop: '8px' }}>
                      <Row gutter={16}>
                        <Col span={12}>
                          <Text type="secondary" style={{ fontSize: '12px' }}>像素密度:</Text>
                          <div>
                            <Text strong>{pixelDensity} PPI</Text>
                          </div>
                        </Col>
                        <Col span={12}>
                          <Text type="secondary" style={{ fontSize: '12px' }}>转换比例:</Text>
                          <div>
                            <Text strong>{conversionRatio.toFixed(3)} mm/px</Text>
                          </div>
                        </Col>
                      </Row>
                      <div style={{ marginTop: '8px' }}>
                        <Text type="secondary" style={{ fontSize: '12px' }}>映射比例:</Text>
                        <div>
                          <Text strong>
                            {renderData.width && renderData.canvasWidth ?
                              (renderData.canvasWidth / renderData.width).toFixed(1) : 'N/A'} px/mm
                          </Text>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 数据信息 */}
                  <div style={{ background: '#f6ffed', padding: '12px', borderRadius: '6px' }}>
                    <Text strong style={{ color: '#52c41a' }}>数据信息</Text>
                    <div style={{ marginTop: '8px' }}>
                      <Row gutter={16}>
                        <Col span={12}>
                          <Text type="secondary" style={{ fontSize: '12px' }}>元素数量:</Text>
                          <div>
                            <Text strong>{renderData.data ? renderData.data.length : 0}</Text>
                          </div>
                        </Col>
                        <Col span={12}>
                          <Text type="secondary" style={{ fontSize: '12px' }}>数据格式:</Text>
                          <div>
                            <Text strong>XPrinter JSON</Text>
                          </div>
                        </Col>
                      </Row>
                    </div>
                  </div>

                  {/* 尺寸关系说明 */}
                  <div style={{ background: '#fff7e6', padding: '12px', borderRadius: '6px', marginTop: '8px' }}>
                    <Text strong style={{ color: '#fa8c16' }}>尺寸关系说明</Text>
                    <div style={{ marginTop: '8px', fontSize: '12px' }}>
                      <div style={{ marginBottom: '4px' }}>
                        <Text type="secondary">• 物理尺寸：标签的实际打印尺寸（毫米）</Text>
                      </div>
                      <div style={{ marginBottom: '4px' }}>
                        <Text type="secondary">• 显示尺寸：在网页中的显示尺寸（像素）</Text>
                      </div>
                      <div style={{ marginBottom: '4px' }}>
                        <Text type="secondary">• 转换公式：显示像素 = 物理毫米 ÷ {conversionRatio.toFixed(3)}</Text>
                      </div>
                      <div>
                        <Text type="secondary">• 映射比例：用于在网页中按比例显示标签</Text>
                      </div>
                    </div>
                  </div>
                </Space>
              </div>
            ) : (
              <div style={{ 
                textAlign: 'center', 
                padding: '60px 20px',
                color: '#999'
              }}>
                <Text type="secondary">
                  请输入数据并点击渲染按钮查看效果
                </Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default TestRenderer;
