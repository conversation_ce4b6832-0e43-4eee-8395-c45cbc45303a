import React, { useState, useCallback } from 'react';
import { Upload, Button, message, Card, Image, Space, Typography, Alert, InputNumber, Select, Row, Col, Divider } from 'antd';
import { InboxOutlined, UploadOutlined, DeleteOutlined, EyeOutlined, MobileOutlined } from '@ant-design/icons';
import textInApi from '../services/textinApi';
import llmVisionApi from '../services/llmVisionApi';

const { Dragger } = Upload;
const { Title, Text } = Typography;
const { Option } = Select;

const ImageUpload = ({ onUploadSuccess, onUploadError, loading, setLoading, apiMode = 'textin' }) => {
  const [fileList, setFileList] = useState([]);
  const [previewImage, setPreviewImage] = useState('');
  const [previewVisible, setPreviewVisible] = useState(false);

  // 像素密度配置
  const [pixelDensity, setPixelDensity] = useState(414); // 默认小米13的PPI
  const [customPPI, setCustomPPI] = useState(false);

  // 常见手机型号的PPI预设
  const phonePresets = [
    { label: '小米13', value: 414, brand: 'Xiaomi' },
    { label: '小米12', value: 419, brand: 'Xiaomi' },
    { label: 'iPhone 15 Pro', value: 460, brand: 'Apple' },
    { label: 'iPhone 14', value: 460, brand: 'Apple' },
    { label: 'Samsung Galaxy S23', value: 425, brand: 'Samsung' },
    { label: 'Huawei P60 Pro', value: 440, brand: 'Huawei' },
    { label: '自定义', value: 'custom', brand: 'Custom' }
  ];

  // 支持的文件类型
  const supportedTypes = [
    'image/png', 'image/jpg', 'image/jpeg', 'image/bmp', 'image/tiff', 'image/webp',
    'application/pdf'
  ];

  const beforeUpload = useCallback((file) => {
    console.log('选择文件:', file);

    // 验证文件
    const validation = textInApi.validateFile(file);
    if (!validation.valid) {
      message.error(validation.error);
      return false;
    }

    // 检查是否已有文件
    if (fileList.length > 0) {
      message.warning('请先删除当前文件再上传新文件');
      return false;
    }

    // 创建预览URL
    const previewUrl = URL.createObjectURL(file);

    const newFile = {
      uid: file.uid,
      name: file.name,
      status: 'done',
      originFileObj: file,
      url: previewUrl,
      size: file.size,
      type: file.type
    };

    setFileList([newFile]);
    message.success('文件选择成功');

    return false; // 阻止自动上传
  }, [fileList]);

  const handleRemove = useCallback((file) => {
    console.log('删除文件:', file);

    // 释放预览URL
    if (file.url && file.url.startsWith('blob:')) {
      URL.revokeObjectURL(file.url);
    }

    setFileList([]);
    message.info('文件已删除');
  }, []);

  const handlePreview = useCallback((file) => {
    console.log('预览文件:', file);

    if (file.type.startsWith('image/')) {
      setPreviewImage(file.url);
      setPreviewVisible(true);
    } else {
      message.info('该文件类型不支持预览');
    }
  }, []);

  // 处理手机型号选择
  const handlePhonePresetChange = useCallback((value) => {
    if (value === 'custom') {
      setCustomPPI(true);
      setPixelDensity(300); // 自定义时的默认值
    } else {
      setCustomPPI(false);
      setPixelDensity(value);
    }
  }, []);

  // 处理自定义PPI输入
  const handleCustomPPIChange = useCallback((value) => {
    if (value && value > 0) {
      setPixelDensity(value);
    }
  }, []);

  const handleRecognize = useCallback(async () => {
    if (fileList.length === 0) {
      message.warning('请先选择文件');
      return;
    }

    const file = fileList[0].originFileObj;
    if (!file) {
      message.error('文件对象不存在');
      return;
    }

    // 验证像素密度
    if (!pixelDensity || pixelDensity <= 0) {
      message.error('请设置有效的像素密度');
      return;
    }

    setLoading(true);

    try {
      console.log(`开始使用 ${apiMode === 'llm' ? '大模型' : 'TextIn'} 识别文件:`, file.name);
      console.log('使用像素密度:', pixelDensity, 'PPI');
      message.loading(`正在使用${apiMode === 'llm' ? '大模型' : 'TextIn API'}识别中，请稍候...`, 0);

      let result;

      if (apiMode === 'llm') {
        // 使用大模型API
        // 将文件转换为base64，并获取图片尺寸
        const { base64, size } = await new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = (e) => {
            const dataUrl = e.target.result;
            const img = document.createElement('img');
            img.onload = () => {
              resolve({
                base64: dataUrl,
                size: { width: img.naturalWidth, height: img.naturalHeight }
              });
            };
            img.onerror = reject;
            img.src = dataUrl;
          };
          reader.onerror = reject;
          reader.readAsDataURL(file);
        });

        console.log('图片识别尺寸:', size);
        console.log('像素到毫米转换比例:', `1 inch = ${pixelDensity} pixels = 25.4 mm`);

        result = await llmVisionApi.recognizeImage(base64, size);

        // 在结果中添加像素密度信息，供后续转换使用
        if (result && result.result) {
          result.result.pixelDensity = pixelDensity;
          result.result.conversionRatio = 25.4 / pixelDensity; // mm per pixel
        }
      } else {
        // 使用TextIn API
        result = await textInApi.recognizeFile(file);

        // 为TextIn结果也添加像素密度信息
        if (result && result.result) {
          result.result.pixelDensity = pixelDensity;
          result.result.conversionRatio = 25.4 / pixelDensity; // mm per pixel
        }
      }

      message.destroy();
      message.success('识别完成！');

      if (onUploadSuccess) {
        onUploadSuccess(result, file);
      }
    } catch (error) {
      console.error('识别失败:', error);
      message.destroy();
      message.error(`识别失败: ${error.message}`);

      if (onUploadError) {
        onUploadError(error);
      }
    } finally {
      setLoading(false);
    }
  }, [fileList, setLoading, onUploadSuccess, onUploadError, apiMode, pixelDensity]);

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const uploadProps = {
    name: 'file',
    multiple: false,
    accept: supportedTypes.join(','),
    fileList: fileList,
    beforeUpload: beforeUpload,
    onRemove: handleRemove,
    onPreview: handlePreview,
    showUploadList: {
      showPreviewIcon: true,
      showRemoveIcon: true,
      showDownloadIcon: false
    }
  };

  return (
    <div style={{ width: '100%' }}>
      <Card title="文件上传" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }} size="middle">
          <Alert
            message="支持的文件格式"
            description="图片格式: PNG, JPG, JPEG, BMP, TIFF, WEBP | 文档格式: PDF"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Dragger {...uploadProps} style={{ padding: '20px' }}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
            </p>
            <p className="ant-upload-text">
              点击或拖拽文件到此区域上传
            </p>
            <p className="ant-upload-hint">
              支持单个文件上传，文件大小不超过500MB
            </p>
          </Dragger>

          {fileList.length > 0 && (
            <Card size="small" title="已选择文件">
              <Space direction="vertical" style={{ width: '100%' }}>
                {fileList.map(file => (
                  <div key={file.uid} style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px',
                    border: '1px solid #d9d9d9',
                    borderRadius: '6px'
                  }}>
                    <Space>
                      <Text strong>{file.name}</Text>
                      <Text type="secondary">({formatFileSize(file.size)})</Text>
                    </Space>
                    <Space>
                      {file.type.startsWith('image/') && (
                        <Button
                          icon={<EyeOutlined />}
                          size="small"
                          onClick={() => handlePreview(file)}
                        >
                          预览
                        </Button>
                      )}
                      <Button
                        icon={<DeleteOutlined />}
                        size="small"
                        danger
                        onClick={() => handleRemove(file)}
                      >
                        删除
                      </Button>
                    </Space>
                  </div>
                ))}
              </Space>
            </Card>
          )}

          {/* 像素密度配置 */}
          <Card size="small" title={
            <Space>
              <MobileOutlined />
              <span>像素密度配置</span>
            </Space>
          }>
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              <Alert
                message="像素到物理尺寸转换"
                description="选择手机型号或自定义像素密度(PPI)，用于将图片像素尺寸转换为实际物理尺寸(毫米)。"
                type="info"
                showIcon
                style={{ marginBottom: 8 }}
              />

              <Row gutter={16}>
                <Col span={12}>
                  <Text strong>手机型号:</Text>
                  <Select
                    style={{ width: '100%', marginTop: 4 }}
                    placeholder="选择手机型号"
                    value={customPPI ? 'custom' : pixelDensity}
                    onChange={handlePhonePresetChange}
                  >
                    {phonePresets.map(preset => (
                      <Option key={preset.value} value={preset.value}>
                        {preset.label} {preset.value !== 'custom' && `(${preset.value} PPI)`}
                      </Option>
                    ))}
                  </Select>
                </Col>

                <Col span={12}>
                  <Text strong>像素密度 (PPI):</Text>
                  <InputNumber
                    style={{ width: '100%', marginTop: 4 }}
                    placeholder="输入PPI值"
                    value={pixelDensity}
                    onChange={handleCustomPPIChange}
                    disabled={!customPPI}
                    min={50}
                    max={1000}
                    precision={0}
                  />
                </Col>
              </Row>

              <div style={{
                background: '#f6f6f6',
                padding: '8px 12px',
                borderRadius: '4px',
                fontSize: '12px',
                color: '#666'
              }}>
                <Text type="secondary">
                  转换比例: 1英寸 = {pixelDensity} 像素 = 25.4 毫米
                  {pixelDensity > 0 && ` (1像素 ≈ ${(25.4 / pixelDensity).toFixed(3)}毫米)`}
                </Text>
              </div>
            </Space>
          </Card>

          <Button
            type="primary"
            icon={<UploadOutlined />}
            size="large"
            loading={loading}
            disabled={fileList.length === 0 || !pixelDensity || pixelDensity <= 0}
            onClick={handleRecognize}
            style={{ width: '100%' }}
          >
            {loading ? '识别中...' : '开始识别'}
          </Button>
        </Space>
      </Card>

      {/* 图片预览模态框 */}
      <Image
        width={200}
        style={{ display: 'none' }}
        src={previewImage}
        preview={{
          visible: previewVisible,
          src: previewImage,
          onVisibleChange: (visible) => {
            setPreviewVisible(visible);
            if (!visible) {
              setPreviewImage('');
            }
          },
        }}
      />
    </div>
  );
};

export default ImageUpload;
