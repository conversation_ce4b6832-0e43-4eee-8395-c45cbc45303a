import React, { useState } from 'react';
import { Card, Typography, Space, Tag, Divider, Input, message, Switch } from 'antd';
import { QRCodeSVG } from 'qrcode.react';
import BarcodeRenderer from './BarcodeRenderer';
import EditableText from './EditableText';

const { Title, Text, Paragraph } = Typography;

// 元素类型映射
const ELEMENT_TYPE_NAMES = {
  '1': '文本',
  '2': '条形码',
  '3': '画布',
  '4': '线条',
  '5': 'Logo',
  '6': '图片',
  '7': '二维码',
  '8': '圆形',
  '9': '时间',
  '10': '表格',
  '11': '矩形'
};

const LabelRenderer = ({
  data,
  canvasWidth = 800,
  canvasHeight = 600,
  onDataChange,
  pixelDensity = 203,
  conversionRatio = 0.125
}) => {
  // 处理新的数据格式：{width, height, data} 或 旧格式：直接数组
  const processedData = React.useMemo(() => {
    if (!data) return { elements: [], canvasWidthMm: 50, canvasHeightMm: 30 };

    if (Array.isArray(data)) {
      // 旧格式：直接是数组
      return { elements: data, canvasWidthMm: 50, canvasHeightMm: 30 };
    } else if (data.data && Array.isArray(data.data)) {
      // 新格式：{width, height, data}
      return {
        elements: data.data,
        canvasWidthMm: data.width || 50,
        canvasHeightMm: data.height || 30
      };
    } else {
      console.warn('未知的数据格式:', data);
      return { elements: [], canvasWidthMm: 50, canvasHeightMm: 30 };
    }
  }, [data]);

  const [localData, setLocalData] = useState(processedData.elements);
  const [showElementList, setShowElementList] = useState(false); // 控制是否显示元素列表

  // 当外部数据变化时更新本地数据
  React.useEffect(() => {
    setLocalData(processedData.elements);
  }, [processedData.elements]);

  // 更新元素内容
  const updateElementContent = (index, newContent) => {
    const newData = [...localData];
    newData[index] = { ...newData[index], content: newContent };
    setLocalData(newData);

    // 通知父组件数据变化
    if (onDataChange) {
      onDataChange(newData);
    }
  };

  if (!localData || !Array.isArray(localData) || localData.length === 0) {
    return (
      <Card title="标签预览">
        <Text type="secondary">暂无数据</Text>
      </Card>
    );
  }

  // 查找画布元素，获取XPrinter数据中的画布尺寸（毫米单位）
  const canvasElement = localData.find(item => item.elementType === '3' || item.elementType === 3);

  // 获取XPrinter数据中的画布尺寸（毫米）
  let canvasWidthMm, canvasHeightMm;

  // 优先使用转换器结果中的画布尺寸
  if (processedData.width && processedData.height) {
    // 从转换器结果获取画布尺寸
    canvasWidthMm = parseFloat(processedData.width);
    canvasHeightMm = parseFloat(processedData.height);
    console.log('✅ 使用转换器结果中的画布尺寸:', canvasWidthMm, 'x', canvasHeightMm, 'mm');
  } else if (processedData.canvasWidthMm && processedData.canvasHeightMm) {
    canvasWidthMm = processedData.canvasWidthMm;
    canvasHeightMm = processedData.canvasHeightMm;
    console.log('使用processedData中的画布尺寸:', canvasWidthMm, 'x', canvasHeightMm, 'mm');
  } else if (canvasElement && canvasElement.width && canvasElement.height) {
    // 从画布元素获取毫米尺寸
    canvasWidthMm = parseFloat(canvasElement.width);
    canvasHeightMm = parseFloat(canvasElement.height);
    console.log('使用画布元素中的画布尺寸:', canvasWidthMm, 'x', canvasHeightMm, 'mm');
  } else {
    // 如果没有画布元素，根据所有元素的分布推算画布尺寸（毫米）
    const elements = localData.filter(item => item.elementType !== '3' && item.elementType !== 3);
    if (elements.length > 0) {
      const maxX = Math.max(...elements.map(el => parseFloat(el.x || 0) + parseFloat(el.width || 0)));
      const maxY = Math.max(...elements.map(el => parseFloat(el.y || 0) + parseFloat(el.height || 0)));
      canvasWidthMm = maxX + 5; // 添加5mm边距
      canvasHeightMm = maxY + 5;
      console.log('根据元素分布推算画布尺寸:', canvasWidthMm, 'x', canvasHeightMm, 'mm');
    } else {
      // 默认画布尺寸（毫米）
      canvasWidthMm = 50; // 50mm
      canvasHeightMm = 30; // 30mm
      console.log('使用默认画布尺寸:', canvasWidthMm, 'x', canvasHeightMm, 'mm');
    }
  }

  // Web显示的目标尺寸（像素）
  const targetDisplayWidth = canvasWidth;   // 传入的显示宽度
  const targetDisplayHeight = canvasHeight; // 传入的显示高度

  // 计算毫米到像素的映射比例
  const scaleX = targetDisplayWidth / canvasWidthMm;   // px/mm
  const scaleY = targetDisplayHeight / canvasHeightMm; // px/mm

  // 使用相同的比例保持宽高比
  let mmToPxScale = Math.min(scaleX, scaleY);

  // 修复TextIn API的映射比例问题
  // 当映射比例过小时（通常发生在TextIn处理大文档时），使用更合理的比例
  if (mmToPxScale < 1) {
    console.warn('检测到映射比例过小，应用修复策略');

    // 计算一个更合理的映射比例
    // 目标：让画布在800x600的区域内合理显示
    const maxDisplayWidth = 800;
    const maxDisplayHeight = 600;

    const reasonableScaleX = maxDisplayWidth / canvasWidthMm;
    const reasonableScaleY = maxDisplayHeight / canvasHeightMm;
    const reasonableScale = Math.min(reasonableScaleX, reasonableScaleY);

    // 使用合理的比例，但不要太大
    mmToPxScale = Math.min(reasonableScale, 5); // 最大5px/mm

    console.log('修复后的映射比例:', mmToPxScale.toFixed(4), 'px/mm');
  }

  // 实际显示尺寸（保持比例）
  const actualCanvasWidth = canvasWidthMm * mmToPxScale;
  const actualCanvasHeight = canvasHeightMm * mmToPxScale;

  console.log('=== LabelRenderer 调试信息 ===');
  console.log('画布元素:', canvasElement);
  console.log('XPrinter画布尺寸(mm):', canvasWidthMm, 'x', canvasHeightMm);
  console.log('目标显示尺寸(px):', targetDisplayWidth, 'x', targetDisplayHeight);
  console.log('scaleX:', scaleX.toFixed(4), 'px/mm');
  console.log('scaleY:', scaleY.toFixed(4), 'px/mm');
  console.log('最终映射比例:', mmToPxScale.toFixed(4), 'px/mm');
  console.log('实际显示尺寸(px):', actualCanvasWidth.toFixed(0), 'x', actualCanvasHeight.toFixed(0));

  // 检查画布尺寸是否合理
  if (canvasWidthMm > 200 || canvasHeightMm > 200) {
    console.warn('⚠️ 检测到异常大的画布尺寸，可能是TextIn数据转换问题');
    console.warn('画布尺寸:', canvasWidthMm, 'x', canvasHeightMm, 'mm');
  }

  // 检查是否是异常的映射比例
  if (mmToPxScale < 1) {
    console.warn('⚠️ 映射比例过小，可能导致渲染问题！');
    console.warn('这通常发生在TextIn API处理大文档时');
  }

  // 移除缩放逻辑，直接使用原始尺寸以实现1:1渲染
  // 原始缩放逻辑已被注释掉，以实现所见即所得
  /*
  const maxDisplayWidth = 800;
  const maxDisplayHeight = 600;
  const scaleX = Math.min(maxDisplayWidth / actualCanvasWidth, 1);
  const scaleY = Math.min(maxDisplayHeight / actualCanvasHeight, 1);
  const scale = Math.min(scaleX, scaleY);
  */
  const scale = 1; // 强制1:1渲染
  const displayWidth = actualCanvasWidth;
  const displayHeight = actualCanvasHeight;

  // 添加详细的调试信息（移动到scale定义之后）
  console.log('=== 详细调试信息 ===');
  console.log('原始数据:', localData);
  console.log('画布尺寸:', { actualCanvasWidth, actualCanvasHeight });
  console.log('缩放比例:', scale);
  console.log('显示尺寸:', { displayWidth, displayHeight });

  // 分析每个文本元素的坐标
  const textElements = localData.filter(item => String(item.elementType) === '1');
  console.log('文本元素分析:');
  textElements.forEach((element, index) => {
    console.log(`文本${index + 1}: "${element.content}"`);
    console.log(`  原始坐标: (${element.x}, ${element.y})`);
    console.log(`  原始尺寸: ${element.width} x ${element.height}`);
    console.log(`  字体大小: ${element.textSize}`);
    console.log(`  渲染坐标: (${parseInt(element.x) * scale}, ${parseInt(element.y) * scale})`);
    console.log(`  渲染尺寸: ${parseInt(element.width) * scale} x ${parseInt(element.height) * scale}`);
    console.log('---');
  });

  // 过滤掉画布元素，只渲染其他元素
  const renderElements = localData.filter(item => item.elementType !== '3' && item.elementType !== 3);

  const renderElement = (element, renderIndex) => {
    // 找到元素在原始数据中的真实索引
    const actualIndex = localData.findIndex(item => item === element);

    // 从XPrinter数据获取毫米坐标和尺寸
    const xMm = parseFloat(element.x || 0);
    const yMm = parseFloat(element.y || 0);
    const widthMm = parseFloat(element.width || 10);
    const heightMm = parseFloat(element.height || 5);

    // 使用映射比例将毫米转换为像素
    const xPx = xMm * mmToPxScale;
    const yPx = yMm * mmToPxScale;
    const widthPx = widthMm * mmToPxScale;
    const heightPx = heightMm * mmToPxScale;

    // 应用显示缩放（用于适应不同屏幕尺寸）
    const x = xPx * scale;
    const y = yPx * scale;
    const width = widthPx * scale;
    const height = heightPx * scale;
    const rotation = parseInt(element.rotationAngle) || 0;

    // 使用元素的实际字体大小，并根据缩放比例调整
    let actualFontSize = parseFloat(element.textSize || '12.0');

    // 修复TextIn API的字体大小问题
    // 当字体大小过小时（通常发生在TextIn处理时），使用更合理的字体大小
    if (actualFontSize < 1) {
      console.warn('检测到字体大小过小:', actualFontSize, '应用修复策略');
      actualFontSize = actualFontSize * mmToPxScale; // 将毫米字体大小转换为像素
      console.log('修复后的字体大小:', actualFontSize);
    }

    const scaledFontSize = actualFontSize * scale;
    const fontSize = Math.max(8, Math.min(scaledFontSize, 48)); // 字体大小范围：8px-48px

    const baseStyle = {
      position: 'absolute',
      left: `${x}px`,
      top: `${y}px`,
      width: `${width}px`,
      height: `${height}px`,
      transform: rotation !== 0 ? `rotate(${rotation}deg)` : 'none',
      transformOrigin: 'top left',
      border: '1px dashed #ccc',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'flex-start', // 简单使用左对齐，位置由坐标决定
      fontSize: `${fontSize}px`,
      overflow: 'hidden',
      boxSizing: 'border-box'
    };

    // 添加调试日志
    console.log(`渲染元素: 类型=${element.elementType}, 内容="${element.content}", 索引=${renderIndex}`);
    console.log(`  原始坐标(mm): (${xMm}, ${yMm}), 原始尺寸(mm): ${widthMm}x${heightMm}`);
    console.log(`  原始字体大小: ${element.textSize}`);
    console.log(`  映射比例: ${mmToPxScale.toFixed(4)} px/mm`);
    console.log(`  映射后坐标(px): (${xPx.toFixed(1)}, ${yPx.toFixed(1)}), 映射后尺寸(px): ${widthPx.toFixed(1)}x${heightPx.toFixed(1)}`);
    console.log(`  最终坐标(px): (${x.toFixed(1)}, ${y.toFixed(1)}), 最终尺寸(px): ${width.toFixed(1)}x${height.toFixed(1)}`);
    console.log(`  最终字体大小: ${fontSize.toFixed(1)}px, 显示缩放: ${scale}`);

    switch (String(element.elementType)) {
      case '1': // 文本
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(24, 144, 255, 0.1)',
              borderColor: '#1890ff',
              padding: '2px',
              textAlign: 'left', // 简单使用左对齐
              fontWeight: element.bold === 'true' ? 'bold' : 'normal',
              fontStyle: element.italic === 'true' ? 'italic' : 'normal',
              textDecoration: element.underline === 'true' ? 'underline' : 'none'
            }}
            title={`文本: ${element.content || '空文本'} 位置:(${element.x}, ${element.y}) 尺寸:${element.width}x${element.height} (点击编辑)`}
          >
            <EditableText
              value={element.content || ''}
              onChange={(newContent) => updateElementContent(actualIndex, newContent)}
              placeholder="点击编辑文本"
              style={{
                fontSize: 'inherit',
                fontWeight: 'inherit',
                fontStyle: 'inherit',
                textDecoration: 'inherit',
                textAlign: 'left',
                width: '100%'
              }}
              textStyle={{
                fontSize: 'inherit',
                lineHeight: '1.2',
                wordBreak: 'normal',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
              }}
            />
          </div>
        );

      case '2': // 条形码
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(82, 196, 26, 0.1)',
              borderColor: '#52c41a',
              padding: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            title={`条形码: ${element.content || '123456789'} (点击编辑)`}
          >
            <div style={{ width: '100%', height: '100%', position: 'relative' }}>
              <BarcodeRenderer
                value={element.content || '123456789'}
                format={element.barcodeType || '1'}
                width={Math.max(1, Math.floor(width / 80))}
                height={Math.max(30, Math.floor(height * 0.7))}
                displayValue={element.showText !== 'false'}
                fontSize={Math.max(8, Math.floor(fontSize * 0.8))}
                textAlign={element.textAlignment === '1' ? 'left' :
                  element.textAlignment === '3' ? 'right' : 'center'}
                style={{
                  width: '100%',
                  height: '100%'
                }}
              />
              <div
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  cursor: 'pointer',
                  background: 'transparent',
                  zIndex: 1
                }}
                onClick={() => {
                  const newContent = prompt('请输入条码内容:', element.content || '123456789');
                  if (newContent !== null && newContent !== element.content) {
                    updateElementContent(index, newContent);
                  }
                }}
              />
            </div>
          </div>
        );

      case '7': // 二维码
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(250, 173, 20, 0.1)',
              borderColor: '#faad14',
              padding: '4px',
              position: 'relative'
            }}
            title={`二维码: ${element.content || 'QR Code'} (点击编辑)`}
          >
            <div style={{
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              {width > 30 && height > 30 ? (
                <QRCodeSVG
                  value={element.content || 'QR Code'}
                  size={Math.min(width - 8, height - 8)}
                  level="M"
                />
              ) : (
                <div style={{
                  width: '80%',
                  height: '80%',
                  backgroundColor: '#000',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '6px'
                }}>
                  QR
                </div>
              )}
            </div>
            <div
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                cursor: 'pointer',
                background: 'transparent'
              }}
              onClick={() => {
                const newContent = prompt('请输入二维码内容:', element.content || 'QR Code');
                if (newContent !== null && newContent !== element.content) {
                  updateElementContent(index, newContent);
                }
              }}
            />
          </div>
        );

      case '6': // 图片
        console.log(`渲染图片元素: 内容=${element.content ? element.content.substring(0, 50) + '...' : '无内容'}`);
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(114, 46, 209, 0.1)',
              borderColor: '#722ed1'
            }}
            title="图片"
          >
            {element.content && element.content.startsWith('data:image') ? (
              <img
                src={element.content}
                alt="识别的图片"
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain'
                }}
              />
            ) : (
              // 如果没有有效图片内容，显示空白而不是"图片"文字
              <div style={{ width: '100%', height: '100%', backgroundColor: 'transparent' }} />
            )}
          </div>
        );

      case '10': // 表格
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(235, 47, 6, 0.1)',
              borderColor: '#eb2f06',
              padding: '2px'
            }}
            title="表格"
          >
            <div style={{
              width: '100%',
              height: '100%',
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(20px, 1fr))',
              gridTemplateRows: 'repeat(auto-fit, minmax(10px, 1fr))',
              gap: '1px',
              backgroundColor: '#ccc'
            }}>
              {Array.from({ length: 6 }, (_, i) => (
                <div
                  key={i}
                  style={{
                    backgroundColor: 'white',
                    fontSize: '6px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  {i < 3 ? 'H' : 'D'}
                </div>
              ))}
            </div>
          </div>
        );

      default:
        return (
          <div
            key={renderIndex}
            style={{
              ...baseStyle,
              backgroundColor: 'rgba(140, 140, 140, 0.1)',
              borderColor: '#8c8c8c'
            }}
            title={`${ELEMENT_TYPE_NAMES[element.elementType] || '未知类型'}`}
          >
            <Text style={{ fontSize: 'inherit' }}>
              {ELEMENT_TYPE_NAMES[element.elementType] || '未知'}
            </Text>
          </div>
        );
    }
  };

  // 主渲染区域样式
  const previewAreaStyle = {
    border: '1px solid #d9d9d9',
    position: 'relative',
    overflow: 'auto', // 当内容超出时显示滚动条
    maxWidth: 800, // 限制最大宽度
    maxHeight: '70vh', // 限制最大高度，避免过高
    backgroundColor: '#f5f5f5',
    margin: '0 auto', // 居中显示
  };

  const canvasStyle = {
    position: 'relative',
    width: `${displayWidth}px`,
    height: `${displayHeight}px`,
    backgroundColor: 'white',
    boxShadow: '0 0 10px rgba(0,0,0,0.1)',
    margin: 'auto' // 在滚动容器内居中
  };

  return (
    <Card title="标签预览" style={{ width: '100%' }}>
      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        {/* 画布信息 */}
        <div style={{
          background: '#f8f9fa',
          padding: '12px',
          borderRadius: '6px',
          border: '1px solid #e9ecef'
        }}>
          <Text strong style={{ color: '#1890ff', fontSize: '14px' }}>画布信息</Text>
          <div style={{ marginTop: '8px' }}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              {/* 物理尺寸和显示尺寸 */}
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>物理尺寸:</Text>
                  <div>
                    <Text strong>{processedData.canvasWidthMm}mm × {processedData.canvasHeightMm}mm</Text>
                  </div>
                </div>
                <div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>显示尺寸:</Text>
                  <div>
                    <Text strong>{actualCanvasWidth}px × {actualCanvasHeight}px</Text>
                  </div>
                </div>
                <div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>显示比例:</Text>
                  <div>
                    <Text strong>{(scale * 100).toFixed(1)}%</Text>
                  </div>
                </div>
              </div>

              {/* 像素密度和转换信息 */}
              <div style={{
                background: '#f0f9ff',
                padding: '8px',
                borderRadius: '4px',
                marginTop: '8px'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>像素密度:</Text>
                    <div>
                      <Text strong style={{ color: '#1890ff' }}>{pixelDensity} PPI</Text>
                    </div>
                  </div>
                  <div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>转换比例:</Text>
                    <div>
                      <Text strong style={{ color: '#1890ff' }}>{conversionRatio.toFixed(3)} mm/px</Text>
                    </div>
                  </div>
                  <div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>映射比例:</Text>
                    <div>
                      <Text strong style={{ color: '#1890ff' }}>
                        {processedData.canvasWidthMm && actualCanvasWidth ?
                          (actualCanvasWidth / processedData.canvasWidthMm).toFixed(1) : 'N/A'} px/mm
                      </Text>
                    </div>
                  </div>
                </div>
              </div>
            </Space>
          </div>
        </div>

        {/* 元素统计 */}
        <div>
          <Text strong>元素统计: </Text>
          <Space wrap>
            {Object.entries(
              renderElements.reduce((acc, item) => {
                const typeName = ELEMENT_TYPE_NAMES[item.elementType] || '未知';
                acc[typeName] = (acc[typeName] || 0) + 1;
                return acc;
              }, {})
            ).map(([type, count]) => (
              <Tag key={type} color="blue">
                {type}: {count}
              </Tag>
            ))}
          </Space>
        </div>

        <Divider />

        {/* 画布渲染区域 */}
        <div style={previewAreaStyle}>
          <div style={canvasStyle}>
            {renderElements.map((element, index) => renderElement(element, index))}
          </div>

          {renderElements.length === 0 && (
            <div
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                textAlign: 'center'
              }}
            >
              <Text type="secondary">暂无识别到的元素</Text>
            </div>
          )}
        </div>

        {/* 元素列表 */}
        {renderElements.length > 0 && (
          <Card size="small" title="元素列表">
            <Space direction="vertical" style={{ width: '100%' }} size="small">
              {renderElements.map((element, index) => (
                <div
                  key={index}
                  style={{
                    padding: '8px',
                    border: '1px solid #d9d9d9',
                    borderRadius: '4px',
                    backgroundColor: '#fafafa'
                  }}
                >
                  <Space>
                    <Tag color="blue">
                      {ELEMENT_TYPE_NAMES[element.elementType] || '未知'}
                    </Tag>
                    <Text>
                      位置: ({element.x}, {element.y})
                    </Text>
                    <Text>
                      尺寸: {element.width} × {element.height}
                    </Text>
                    {element.content && (
                      <Text type="secondary">
                        内容: {element.content.length > 20
                          ? element.content.substring(0, 20) + '...'
                          : element.content}
                      </Text>
                    )}
                  </Space>
                </div>
              ))}
            </Space>
          </Card>
        )}
      </Space>
    </Card>
  );
};

export default LabelRenderer;
