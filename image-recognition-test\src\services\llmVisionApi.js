/**
 * 大模型视觉API服务
 * 支持多种大模型提供商进行图像识别和XPrinter数据生成
 */

import CryptoJS from 'crypto-js';

class LLMVisionApi {
  constructor() {
    this.providers = {
      spark: {
        name: '星火图片理解',
        baseUrl: 'wss://spark-api.cn-huabei-1.xf-yun.com/v2.1/image',
        model: 'general',
        supportsVision: true,
        authType: 'websocket'
      },
      doubao: {
        name: '豆包火山方舟',
        baseUrl: 'https://ark.cn-beijing.volces.com/api/v3',
        model: 'doubao-1.5-thinking-vision-pro',
        supportsVision: true,
        authType: 'bearer'
      },
      deepseek: {
        name: 'DeepSeek Reasoner',
        baseUrl: 'https://api.deepseek.com',
        model: 'deepseek-reasoner',
        supportsVision: false // DeepSeek目前不支持视觉
      },
      openai: {
        name: 'OpenAI GPT-4V',
        baseUrl: 'https://api.openai.com/v1',
        model: 'gpt-4-vision-preview',
        supportsVision: true
      },
      claude: {
        name: 'Claude 3 Vision',
        baseUrl: 'https://api.anthropic.com/v1',
        model: 'claude-3-sonnet-20240229',
        supportsVision: true
      }
    };

    this.currentProvider = 'spark';
    this.apiKey = '';

    // 星火认证信息
    this.sparkAuth = {
      appId: '',
      apiSecret: '',
      apiKey: ''
    };
  }

  /**
   * 设置API配置
   * @param {string} provider - 提供商名称
   * @param {string|Object} apiKey - API密钥或星火认证对象
   */
  setConfig(provider, apiKey) {
    if (!this.providers[provider]) {
      throw new Error(`不支持的提供商: ${provider}`);
    }
    this.currentProvider = provider;

    if (provider === 'spark' && typeof apiKey === 'object') {
      // 星火使用特殊的认证信息
      this.sparkAuth = apiKey;
    } else {
      this.apiKey = apiKey;
    }
  }

  /**
   * 设置星火认证信息
   * @param {string} appId - APPID
   * @param {string} apiSecret - APISecret
   * @param {string} apiKey - APIKey
   */
  setSparkAuth(appId, apiSecret, apiKey) {
    this.sparkAuth = { appId, apiSecret, apiKey };
    this.currentProvider = 'spark';
    console.log('星火认证信息已设置:', {
      appId: appId ? '***' : '空',
      apiSecret: apiSecret ? '***' : '空',
      apiKey: apiKey ? '***' : '空'
    });
  }

  /**
   * 检查当前认证状态
   * @returns {Object} 认证状态信息
   */
  getAuthStatus() {
    if (this.currentProvider === 'spark') {
      return {
        provider: 'spark',
        configured: !!(this.sparkAuth.appId && this.sparkAuth.apiSecret && this.sparkAuth.apiKey),
        details: {
          appId: this.sparkAuth.appId ? '已配置' : '未配置',
          apiSecret: this.sparkAuth.apiSecret ? '已配置' : '未配置',
          apiKey: this.sparkAuth.apiKey ? '已配置' : '未配置'
        }
      };
    } else {
      return {
        provider: this.currentProvider,
        configured: !!this.apiKey,
        details: {
          apiKey: this.apiKey ? '已配置' : '未配置'
        }
      };
    }
  }

  /**
   * 获取XPrinter格式的提示词模板
   * @returns {string} 提示词
   */
  getXPrinterPrompt() {
    return `你是一个专业的图像识别和布局分析专家。你的任务是分析提供的图片，并以XPrinter JSON格式返回所有识别出的元素（文本、条形码、二维码）。请严格按照以下步骤和要求操作：

**工作流程:**
1.  **元素识别:** 逐一识别图片中的所有独立元素，包括每一行文本、每个条形码和每个二维码。
2.  **精确定位:** 仔细观察每个元素在图片中的实际位置，不要假设文本是左对齐或有固定的布局模式。
3.  **属性提取:** 对于每个元素，提取其内容和属性。
4.  **JSON生成:** 根据提取的属性，生成严格符合XPrinter格式的JSON对象。

**核心要求:**

1.  **坐标系:** 坐标系原点(0,0)在图片的左上角。所有位置(x, y)和尺寸(width, height)都必须是相对于这个原点的像素值。

2.  **精确定位（至关重要）:**
    *   **仔细观察**: 请逐个分析每行文字在图片中的真实位置，不要假设它们都在同一个x坐标。
    *   **\`x\`, \`y\`**: 元素的左上角在图片中的精确像素坐标。请根据视觉观察确定，而不是假设。
    *   **\`width\`, \`height\`**: 元素的精确像素宽度和高度。宽度应该刚好包含文字内容，不要过宽。
    *   **避免重叠**: 确保相邻文本元素的y坐标有足够间距，避免重叠。

3.  **文本样式识别:**
    *   **\`textSize\` (字体大小):** 根据文本在图片中的像素高度来准确估算字体大小。如果一行文字的边界框高度是25像素，那么\`textSize\`应该接近"25.0"。
    *   **\`bold\`, \`italic\`, \`underline\`**: 判断文本样式（"true"或"false"）。

4.  **内容识别:**
    *   \`content\`: 文本的实际内容，或条形码/二维码解码后的字符串。

5.  **格式要求:**
    *   严格按照XPrinter JSON格式返回。
    *   所有JSON字段的值都必须是**字符串**类型。
    *   最终输出**只能包含JSON数组**，不要包含解释文字。

**特别注意 - 位置分析方法:**
- 请逐行分析文字，观察每行文字在图片中的实际起始位置
- 不要假设所有文字都从同一个x坐标开始
- 仔细测量每行文字的实际宽度，不要使用固定宽度
- 确保y坐标反映文字的真实垂直位置

XPrinter元素类型：
- elementType: '1' = 文本
- elementType: '2' = 条形码  
- elementType: '3' = 画布
- elementType: '6' = 图片
- elementType: '7' = 二维码

条形码类型(barcodeType)：
- 1 = CODE_128, 2 = CODE_39, 4 = EAN_13, 5 = EAN_8, 8 = UPC_A

文本对齐(hAlignment)：
- 1 = 左对齐, 2 = 居中对齐, 3 = 右对齐

示例JSON格式：
[
  {
    "elementType": "3",
    "width": "800",
    "height": "600",
    "os": "web",
    "versionCode": "0"
  },
  {
    "elementType": "1",
    "x": "50",
    "y": "20", 
    "width": "150",
    "height": "25",
    "content": "产品名称",
    "textSize": "25.0",
    "bold": "true",
    "italic": "false",
    "underline": "false",
    "hAlignment": "1",
    "rotationAngle": "0"
  }
]

请仔细分析图片中每个元素的实际位置和尺寸，准确识别文本样式，只返回JSON数据。`;
  }

  /**
   * 调用大模型API进行图像识别
   * @param {string} base64Image - base64格式的图片
   * @param {object} imageSize - 图片尺寸 { width, height }
   * @returns {Promise<Object>} 识别结果
   */
  async recognizeImage(base64Image, imageSize = { width: 800, height: 600 }) {
    // 打印当前认证状态用于调试
    const authStatus = this.getAuthStatus();
    console.log('当前认证状态:', authStatus);

    // 检查API密钥配置
    if (this.currentProvider === 'spark') {
      // 星火大模型需要检查sparkAuth对象
      if (!this.sparkAuth.appId || !this.sparkAuth.apiSecret || !this.sparkAuth.apiKey) {
        console.error('星火认证信息不完整:', this.sparkAuth);
        throw new Error('请先配置星火大模型认证信息（APPID、APISecret、APIKey）');
      }
    } else {
      // 其他提供商检查apiKey
      if (!this.apiKey) {
        throw new Error('请先配置API密钥');
      }
    }

    const provider = this.providers[this.currentProvider];

    // 检查提供商是否支持视觉
    if (!provider.supportsVision) {
      throw new Error(`${provider.name} 暂不支持图像识别功能，请选择支持视觉的模型（如OpenAI GPT-4V）`);
    }

    try {
      console.log(`使用 ${provider.name} 进行图像识别...`);

      let response;

      switch (this.currentProvider) {
        case 'spark':
          response = await this.callSparkAPI(base64Image);
          break;
        case 'doubao':
          response = await this.callDoubaoAPI(base64Image);
          break;
        case 'deepseek':
          response = await this.callDeepSeekAPI(base64Image);
          break;
        case 'openai':
          response = await this.callOpenAIAPI(base64Image);
          break;
        case 'claude':
          response = await this.callClaudeAPI(base64Image);
          break;
        default:
          throw new Error(`未实现的提供商: ${this.currentProvider}`);
      }

      return this.parseResponse(response, imageSize);
    } catch (error) {
      console.error('大模型API调用失败:', error);
      throw new Error(`${provider.name} API调用失败: ${error.message}`);
    }
  }

  /**
   * 生成星火API认证URL
   * @returns {string} 认证后的WebSocket URL
   */
  generateSparkAuthUrl() {
    const { appId, apiSecret, apiKey } = this.sparkAuth;

    console.log('星火认证信息:', { appId, apiSecret: apiSecret ? '***' : '空', apiKey: apiKey ? '***' : '空' });

    if (!appId || !apiSecret || !apiKey) {
      throw new Error('星火认证信息不完整，请检查APPID、APISecret和APIKey');
    }

    // 生成RFC1123格式的时间戳
    const date = new Date().toUTCString();

    // 构建签名字符串
    const signatureOrigin = `host: spark-api.cn-huabei-1.xf-yun.com\ndate: ${date}\nGET /v2.1/image HTTP/1.1`;

    console.log('签名原始字符串:', signatureOrigin);

    // 使用HMAC-SHA256生成签名
    const signature = CryptoJS.HmacSHA256(signatureOrigin, apiSecret);
    const signatureBase64 = CryptoJS.enc.Base64.stringify(signature);

    // 构建authorization字符串
    const authorization = `api_key="${apiKey}", algorithm="hmac-sha256", headers="host date request-line", signature="${signatureBase64}"`;
    const authorizationBase64 = btoa(authorization);

    console.log('Authorization字符串:', authorization);

    // 构建最终的WebSocket URL

    const url = `${this.providers.spark.baseUrl}?authorization=${authorizationBase64}&date=${encodeURIComponent(date)}&host=spark-api.cn-huabei-1.xf-yun.com`;

    return url;
  }


  /**
   * 调用星火图片理解API
   * @param {string} base64Image - base64图片
   * @returns {Promise<Object>} API响应
   */
  async callSparkAPI(base64Image) {
    return new Promise((resolve, reject) => {
      try {
        // 提取base64数据（去掉data:image/jpeg;base64,前缀）
        let imageBase64 = base64Image;
        if (imageBase64.startsWith('data:')) {
          imageBase64 = imageBase64.split(',')[1];
        }

        // 生成认证URL
        const wsUrl = this.generateSparkAuthUrl();
        console.log('星火WebSocket连接URL:', wsUrl);

        // 创建WebSocket连接
        const ws = new WebSocket(wsUrl);
        let result = '';

        ws.onopen = () => {
          console.log('星火WebSocket连接已建立');

          // 构建请求数据 - 根据星火图片理解API文档格式
          const requestData = {
            header: {
              app_id: this.sparkAuth.appId,
              uid: 'user_' + Date.now()
            },
            parameter: {
              chat: {
                domain: 'image',
                temperature: 0.1,
                max_tokens: 4096
              }
            },
            payload: {
              message: {
                text: [
                  {
                    role: 'user',
                    content: imageBase64,
                    content_type: 'image'
                  },
                  {
                    role: 'user',
                    content: this.getXPrinterPrompt(),
                    content_type: 'text'
                  }
                ]
              }
            }
          };

          console.log('发送星火API请求:', requestData);
          ws.send(JSON.stringify(requestData));
        };

        ws.onmessage = (event) => {
          try {
            const response = JSON.parse(event.data);
            console.log('星火API响应:', response);

            if (response.header && response.header.code !== 0) {
              reject(new Error(`星火API错误: ${response.header.message}`));
              return;
            }

            if (response.payload && response.payload.choices) {
              const delta = response.payload.choices.text[0];
              if (delta.content) {
                result += delta.content;
              }

              // 检查是否是最后一条消息
              if (response.header.status === 2) {
                ws.close();
                // 模拟OpenAI格式的响应
                resolve({
                  choices: [{
                    message: {
                      content: result
                    }
                  }]
                });
              }
            }
          } catch (error) {
            console.error('解析星火响应失败:', error);

            reject(new Error(`解析响应失败: ${error.message}`));
          }
        };

        ws.onerror = (error) => {
          console.error('星火WebSocket错误:', error);
          reject(new Error('WebSocket连接错误'));
        };

        ws.onclose = (event) => {
          console.log('星火WebSocket连接已关闭:', event.code, event.reason);
          if (event.code !== 1000 && !result) {
            reject(new Error(`WebSocket连接异常关闭: ${event.code} ${event.reason}`));
          }
        };

        // 设置超时
        setTimeout(() => {
          if (ws.readyState === WebSocket.CONNECTING || ws.readyState === WebSocket.OPEN) {
            ws.close();
            reject(new Error('请求超时'));
          }
        }, 30000); // 30秒超时

      } catch (error) {
        console.error('星火API调用失败:', error);
        reject(error);
      }
    });
  }

  /**
   * 调用豆包火山方舟API
   * @param {string} base64Image - base64图片
   * @returns {Promise<Object>} API响应
   */
  async callDoubaoAPI(base64Image) {
    // 确保图片格式正确
    let imageUrl = base64Image;
    if (!imageUrl.startsWith('data:')) {
      imageUrl = `data:image/jpeg;base64,${base64Image}`;
    }

    const requestBody = {
      model: this.providers.doubao.model,
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: this.getXPrinterPrompt()
            },
            {
              type: 'image_url',
              image_url: {
                url: imageUrl
              }
            }
          ]
        }
      ],
      max_tokens: 4000,
      temperature: 0.1,
      stream: false
    };

    console.log('豆包API 请求:', {
      url: `${this.providers.doubao.baseUrl}/chat/completions`,
      model: this.providers.doubao.model,
      imageSize: imageUrl.length
    });

    const response = await fetch(`${this.providers.doubao.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'Accept': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('豆包API 响应状态:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('豆包API 错误响应:', errorText);

      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { error: { message: errorText } };
      }

      throw new Error(`HTTP ${response.status}: ${errorData.error?.message || errorData.message || response.statusText}`);
    }

    const result = await response.json();
    console.log('豆包API 响应:', result);
    return result;
  }

  /**
   * 调用DeepSeek API
   * @param {string} base64Image - base64图片
   * @returns {Promise<Object>} API响应
   */
  async callDeepSeekAPI(base64Image) {
    // 确保图片格式正确
    let imageUrl = base64Image;
    if (!imageUrl.startsWith('data:')) {
      imageUrl = `data:image/jpeg;base64,${base64Image}`;
    }

    const requestBody = {
      model: this.providers.deepseek.model,
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: this.getXPrinterPrompt()
            },
            {
              type: 'image_url',
              image_url: {
                url: imageUrl
              }
            }
          ]
        }
      ],
      max_tokens: 4000,
      temperature: 0.1,
      stream: false
    };

    console.log('DeepSeek API 请求:', {
      url: `${this.providers.deepseek.baseUrl}/chat/completions`,
      model: this.providers.deepseek.model,
      imageSize: imageUrl.length
    });

    const response = await fetch(`${this.providers.deepseek.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'Accept': 'application/json'
      },
      body: JSON.stringify(requestBody)
    });

    console.log('DeepSeek API 响应状态:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('DeepSeek API 错误响应:', errorText);

      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { error: { message: errorText } };
      }

      throw new Error(`HTTP ${response.status}: ${errorData.error?.message || errorData.message || response.statusText}`);
    }

    const result = await response.json();
    console.log('DeepSeek API 响应:', result);
    return result;
  }

  /**
   * 调用OpenAI API
   * @param {string} base64Image - base64图片
   * @returns {Promise<Object>} API响应
   */
  async callOpenAIAPI(base64Image) {
    const response = await fetch(`${this.providers.openai.baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      },
      body: JSON.stringify({
        model: this.providers.openai.model,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: this.getXPrinterPrompt()
              },
              {
                type: 'image_url',
                image_url: {
                  url: base64Image.startsWith('data:') ? base64Image : `data:image/jpeg;base64,${base64Image}`
                }
              }
            ]
          }
        ],
        max_tokens: 4000,
        temperature: 0.1
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`HTTP ${response.status}: ${errorData.error?.message || response.statusText}`);
    }

    return await response.json();
  }

  /**
   * 调用Claude API
   * @param {string} base64Image - base64图片
   * @returns {Promise<Object>} API响应
   */
  async callClaudeAPI(base64Image) {
    // Claude API的实现
    throw new Error('Claude API暂未实现，请使用DeepSeek或OpenAI');
  }

  /**
   * 解析API响应
   * @param {Object} response - API响应
   * @param {object} imageSize - 图片尺寸 { width, height }
   * @returns {Object} 解析后的结果
   */
  parseResponse(response, imageSize) {
    try {
      let content = '';

      if (response.choices && response.choices[0]) {
        content = response.choices[0].message?.content || '';
      }

      // 提取JSON部分
      const jsonMatch = content.match(/\[[\s\S]*\]/);
      if (!jsonMatch) {
        throw new Error('响应中未找到有效的JSON数据');
      }

      const jsonData = JSON.parse(jsonMatch[0]);

      return {
        result: {
          pages: [{
            width: imageSize.width,
            height: imageSize.height,
            elements: jsonData
          }]
        }
      };
    } catch (error) {
      console.error('解析响应失败:', error);
      throw new Error(`解析响应失败: ${error.message}`);
    }
  }

  /**
   * 获取支持的提供商列表
   * @returns {Array} 提供商列表
   */
  getProviders() {
    return Object.entries(this.providers).map(([key, value]) => ({
      key,
      name: value.name,
      model: value.model
    }));
  }
}

// 创建单例实例
const llmVisionApi = new LLMVisionApi();

export default llmVisionApi;
