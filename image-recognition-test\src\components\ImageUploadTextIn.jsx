import React, { useState, useCallback } from 'react';
import { Upload, Button, message, Card, Image, Space, Typography, Alert } from 'antd';
import { InboxOutlined, UploadOutlined, DeleteOutlined, EyeOutlined, FileTextOutlined } from '@ant-design/icons';
import textInApi from '../services/textinApi';

const { Dragger } = Upload;
const { Title, Text } = Typography;

const ImageUploadTextIn = ({ onUploadSuccess, onUploadError, loading, setLoading }) => {
  const [fileList, setFileList] = useState([]);
  const [previewImage, setPreviewImage] = useState('');
  const [previewVisible, setPreviewVisible] = useState(false);

  // 文件上传前的验证
  const beforeUpload = useCallback((file) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件！');
      return false;
    }

    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('图片大小不能超过 10MB！');
      return false;
    }

    return false; // 阻止自动上传
  }, []);

  // 处理文件选择
  const handleChange = useCallback((info) => {
    let newFileList = [...info.fileList];
    
    // 只保留最新的一个文件
    newFileList = newFileList.slice(-1);
    
    // 为文件生成预览URL
    newFileList = newFileList.map(file => {
      if (file.originFileObj && !file.url && !file.preview) {
        file.url = URL.createObjectURL(file.originFileObj);
      }
      return file;
    });

    setFileList(newFileList);
  }, []);

  // 删除文件
  const handleRemove = useCallback((file) => {
    console.log('删除文件:', file);

    // 释放预览URL
    if (file.url && file.url.startsWith('blob:')) {
      URL.revokeObjectURL(file.url);
    }

    setFileList([]);
    setPreviewImage('');
    setPreviewVisible(false);
  }, []);

  // 预览图片
  const handlePreview = useCallback((file) => {
    setPreviewImage(file.url || file.preview);
    setPreviewVisible(true);
  }, []);

  // 识别处理
  const handleRecognize = useCallback(async () => {
    if (fileList.length === 0) {
      message.warning('请先选择文件');
      return;
    }

    const file = fileList[0].originFileObj;
    if (!file) {
      message.error('文件对象不存在');
      return;
    }

    setLoading(true);

    try {
      console.log('开始使用TextIn API识别文件:', file.name);
      message.loading('正在使用TextIn API识别中，请稍候...', 0);

      // 使用TextIn API
      const result = await textInApi.recognizeFile(file);
      
      // 获取图片尺寸（TextIn API需要从结果中获取）
      let imageWidth = 800, imageHeight = 600; // 默认值
      if (result && result.result && result.result.pages && result.result.pages[0]) {
        imageWidth = result.result.pages[0].width || 800;
        imageHeight = result.result.pages[0].height || 600;
      }
      
      // TextIn API使用固定的转换比例（203 PPI）
      const pixelDensity = 203;
      const conversionRatio = 25.4 / pixelDensity;
      
      console.log('TextIn使用固定像素密度转换:', `${pixelDensity} PPI, 转换比例 ${conversionRatio.toFixed(4)} mm/pixel`);
      
      // 为TextIn结果添加转换信息
      if (result && result.result) {
        result.result.pixelDensity = pixelDensity;
        result.result.conversionRatio = conversionRatio;
        result.result.usePhysicalSize = false; // TextIn始终使用像素密度模式
      }

      message.destroy();
      message.success('识别完成！');

      if (onUploadSuccess) {
        onUploadSuccess(result, file);
      }
    } catch (error) {
      console.error('识别失败:', error);
      message.destroy();
      message.error(`识别失败: ${error.message}`);

      if (onUploadError) {
        onUploadError(error);
      }
    } finally {
      setLoading(false);
    }
  }, [fileList, setLoading, onUploadSuccess, onUploadError]);

  return (
    <Space direction="vertical" style={{ width: '100%' }} size="large">
      <Card title="TextIn API 图像识别" size="small">
        <Space direction="vertical" style={{ width: '100%' }} size="middle">
          <Alert
            message="TextIn API 识别"
            description="TextIn API 专门用于文档和表格识别，使用固定的203 PPI像素密度进行转换。适合处理扫描文档和表格类图片。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          {/* 文件上传区域 */}
          <Dragger
            fileList={fileList}
            beforeUpload={beforeUpload}
            onChange={handleChange}
            onRemove={handleRemove}
            onPreview={handlePreview}
            accept="image/*"
            listType="picture"
            maxCount={1}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽图片到此区域上传</p>
            <p className="ant-upload-hint">
              支持单个图片上传，支持 JPG、PNG、GIF 等格式，文件大小不超过 10MB
            </p>
          </Dragger>

          {/* 预览功能 */}
          {fileList.length > 0 && (
            <Space>
              <Button 
                icon={<EyeOutlined />} 
                onClick={() => handlePreview(fileList[0])}
              >
                预览图片
              </Button>
              <Button 
                icon={<DeleteOutlined />} 
                onClick={() => handleRemove(fileList[0])}
                danger
              >
                删除图片
              </Button>
            </Space>
          )}

          {/* TextIn API 配置信息 */}
          <Card size="small" title={
            <Space>
              <FileTextOutlined />
              <span>TextIn API 配置</span>
            </Space>
          }>
            <Space direction="vertical" style={{ width: '100%' }} size="small">
              <div style={{ background: '#f0f9ff', padding: '12px', borderRadius: '6px' }}>
                <Text strong style={{ color: '#1890ff' }}>固定配置参数</Text>
                <div style={{ marginTop: '8px' }}>
                  <div style={{ marginBottom: '4px' }}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>像素密度:</Text>
                    <Text strong style={{ marginLeft: '8px' }}>203 PPI</Text>
                  </div>
                  <div style={{ marginBottom: '4px' }}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>转换比例:</Text>
                    <Text strong style={{ marginLeft: '8px' }}>0.125 mm/pixel</Text>
                  </div>
                  <div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>适用场景:</Text>
                    <Text strong style={{ marginLeft: '8px' }}>文档、表格、扫描图片</Text>
                  </div>
                </div>
              </div>
              
              <Alert
                message="注意事项"
                description="TextIn API 使用固定的转换参数，适合标准文档识别。如需自定义转换比例，请使用大模型识别。"
                type="warning"
                showIcon
                size="small"
              />
            </Space>
          </Card>

          <Button
            type="primary"
            icon={<UploadOutlined />}
            size="large"
            loading={loading}
            disabled={fileList.length === 0}
            onClick={handleRecognize}
            style={{ width: '100%' }}
          >
            {loading ? '识别中...' : '开始识别'}
          </Button>
        </Space>
      </Card>

      {/* 图片预览模态框 */}
      <Image
        width={200}
        style={{ display: 'none' }}
        src={previewImage}
        preview={{
          visible: previewVisible,
          src: previewImage,
          onVisibleChange: (visible) => setPreviewVisible(visible),
        }}
      />
    </Space>
  );
};

export default ImageUploadTextIn;
